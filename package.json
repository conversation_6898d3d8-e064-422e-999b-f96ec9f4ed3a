{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "2.1.5", "@next/third-parties": "15.0.3", "@react-oauth/google": "0.12.1", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "3.12.0", "@sentry/nextjs": "^9.12.0", "@seontechnologies/seon-id-verification": "^1.1.0", "@sumsub/websdk": "2.3.4", "@sumsub/websdk-react": "^2.3.12", "embla-carousel": "8.2.1", "embla-carousel-react": "8.2.1", "framer-motion": "11.5.4", "moment": "2.30.1", "next": "14.2.5", "react": "18.3.1", "react-apple-login": "1.1.6", "react-confetti": "^6.2.3", "react-cookie-consent": "9.0.0", "react-dom": "18.3.1", "react-hot-toast": "2.4.1", "react-icons": "5.3.0", "react-infinite-scroll-component": "6.1.0", "react-pdf": "9.1.1", "react-select": "5.8.0", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "socket.io-client": "4.7.5", "styled-components": "6.1.13", "tailwind-datepicker-react": "1.4.3", "use-debounce": "10.0.3", "uuid": "^11.1.0", "zod": "3.23.8", "zustand": "4.5.5"}, "devDependencies": {"@types/node": "22.1.0", "@types/react": "18.3.3", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.45", "tailwindcss": "^3.4.10"}}