/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/tailwind-datepicker-react/dist/**/*.js",
  ],
  darkMode: "class",
  theme: {
    extend: {
      fontFamily: {
        manrope: ["Manrope", "sans-serif"],
        "geometrics-medium": ["geometrics-medium", "sans-serif"],
        "geometrics-bold": ["geometrics-bold", "sans-serif"],
      },

      colors: {
        white: "var(--fta-whiteColor)",
        black: "var(--fta-blackColor)",
        blue: "var(--fta-blueColor)",
        blue: {
          100: "rgba(24, 119, 242, 1)", //#1877F2
        },

        whiteOpacity: {
          100: "var(--fta-whiteOpacity-100)",
          200: "var(--fta-whiteOpacity-200)",
          300: "#FFF8F5",
          400: "#9D9D9D",
          500: "#rgba(157, 157, 157, 0.3)",
        },

        blackOpacity: {
          100: "var(--fta-blackOpacity-100)",
          150: "var(--fta-blackOpacity-150)",
          200: "var(--fta-blackOpacity-200)",
          250: "var(--fta-blackOpacity-250)",
          300: "var(--fta-blackOpacity-300)",
          350: "var(--fta-blackOpacity-350)",
          400: "var(--fta-blackOpacity-400)",
          500: "var(--fta-blackOpacity-500)",
          550: "var(--fta-blackOpacity-550)",
          600: "var(--fta-blackOpacity-600)",
        },

        primary: {
          100: "rgba(33, 37, 55, 1)", // #212537
          200: "var(--fta-primary-200)",
          250: "var(--fta-primary-250)",
          300: "var(--fta-primary-300)",
          400: "var(--fta-primary-400)",
          500: "var(--fta-primary-500)",
          600: "var(--fta-primary-600)",
          1000: "var(--fta-primary-1000)",
        },

        secondary: {
          300: "var(--fta-secondary-300)",
          500: "var(--fta-secondary-500)",
          800: "var(--fta-secondary-800)",
          1000: "var(--fta-secondary-1000)",
        },

        black: {
          2: "#1D3303",
          3: "rgba(25, 30, 46, 1)", // #191E2E
          4: "rgba(24, 27, 41, 1)", // #181B29
          5: "rgba(23, 28, 40, 1)", // ##171A28
        },

        // background: #1D3303;

        green: {
          100: "rgba(48, 157, 48, 1)", // #309D30
          200: "rgba(17, 115, 17, 1)", // #117311
          500: "rgba(26, 121, 70,1)", // #1A7946
          4: "rgba(126, 148, 136,1)", // #7E9488
          6: "rgba(51, 217, 178, 1)", // #33D9B2
          300: "rgba(51, 204, 51, 1)", // #33CC33
          400: "rgba(48, 157, 48, 0.41)",
          600: "rgba(32, 54, 42, 1)", // #20362A",
          700: "rgb(35, 56, 45, 1)", // #23382D
          800: "rgb(53, 205, 53, 1)", // #35CD35
        },

        pink: {
          1: "var(--pink-1)",
          2: "var(--pink-2)",
          3: "rgba(243, 29, 83, 1)",
          4: "rgba(190, 67, 227, 1)", //#BE43E3
          5: "rgba(147, 134, 152, 1)", //#938698
        },

        orangeShade: {
          1: "rgba(255, 175, 44, 1)", //FFAF2C
          2: "rgba(255, 192, 72, 1)", //#FFC048
        },

        gray: {
          1: "var(--gray-1)",
          2: "rgba(134, 149, 141, 400)", // #86958D
          3: "rgba(58, 62, 77, 1)", // #3A3E4D
          4: "rgba(54, 48, 32, 1)", // #363020
          5: "#30364E",
          6: "#3C4569",
          7: "#BFBFC0",
          8: "rgba(62,62,62,0.3)",
          200: "rgba(61, 68, 98, 1)", // #3D4462
          300: "rgba(112, 112, 112, 1)", //#707070
          400: "rgba(11, 16, 32, 0.82)", // #0B1020D1
          500: "rgba(11, 16, 32, 1)", // #0B1020
          600: "rgba(56, 61, 77, 1)", //#383D4D
          700: "rgba(107, 112, 128, 1)", //#6B7080
          800: "rgba(56, 61, 77, 1)", //#383d4d
          900: "rgba(55, 55, 55, 1)", //#373737
          1000: "rgba(103, 112, 123, 1)", //#67707B
          1100: "rgba(139, 139, 139 , 1)", //8B8B8B
        },

        backDrop: {
          100: "rgba(11, 16, 32, 0.5)",
        },

        red: {
          1: "rgba(255, 76, 62, 1)", //#FF4C3E
        },

        lightGray: {
          1: "rgba(199, 199, 199, 1)",
        },
      },

      maxWidth: {
        mw406: "25.375rem",
        customMaxWidth: "calc(100% + 6px)",
      },
      marginTop: {
        marginTopMinus1: "3.563rem",
      },
      width: {
        customWidth: "calc(100% + 6px)",
      },
      marginTop: {
        marginTopMinus1: "3.563rem",
      },

      backgroundImage: {
        // Gradient Color
        borderBg:
          "linear-gradient(87.99deg, #EBDC03 -4.33%, #FF9321 21.23%, #FF9321 23.32%, #F24C50 47.31%, #8B21B7 100%)",
        profileBorderGradient:
          "linear-gradient(-45deg, rgba(51, 204, 51, 1) 0%, rgb(251 251 251 / 20%) 100%)",
        vipBg: "linear-gradient(180deg, rgba(146, 9, 68, 0) 0%, #8B083F 100%)",
        poupBg:
          "linear-gradient(45deg, transparent 49%, #1a1c1a 49% 51%, transparent 51%), linear-gradient(-45deg, transparent 49%, #1a1c1a 49% 51%, transparent 51%)",

        // Background Image
        checkIcon: 'url("/assets/img/svg/checkbox-icon.svg")',
        bonusBg: 'url("/assets/img/png/bonus-bg.png")',
        FrameBg: 'url("/assets/img/png/frameImg.png")',
        ProfileBtnBg: 'url("/assets/img/svg/ProfileBtnBg.svg")',
        pc1: 'url("/assets/img/jpg/pc-1.jpg")',
        pc2: 'url("/assets/img/jpg/pc-2.jpg")',
        pc3: 'url("/assets/img/jpg/pc-3.jpg")',
        package1: 'url("/assets/img/jpg/package1.jpg")',
        gradientCard: 'url("/assets/img/png/Card.webp")',
        mobileMenuBottom: 'url("/assets/img/png/mobileMenuBottombg.png")',
        profileImgBg: 'url("/assets/img/png/profilebg.png")',
        profileActiveBg: 'url("/assets/img/png/profileActiveBg.png")',
        vipLevelBg: 'url("/assets/img/png/vip-level-bg.png")',
      },

      backgroundSize: {
        "custom-size": "3em 3em", // Custom background size
      },

      dropShadow: {
        custom: "0px 7.58333px 26px rgba(235, 220, 3, 0.6)",
        checkboxShadow: "0 0 0.5rem rgba(26, 121, 70, 1)",
      },

      boxShadow: {
        "custom-sc": "0px 0px 24px 0px rgba(214, 194, 25, 0.5)",
        "custom-gc": "0px 0px 24px 0px rgba(255, 192, 72, 0.5)",
        bottomShadow: "0px 3px 3px -1px #000",
        customBtnshadow1: "inset -3px -4px 7px 0 rgba(255, 255, 255, 0.15)",
        customBtnshadow2: "4px 38px 62px 0 rgba(0, 0, 0, 0.5)",
        gameHoverShadow: "0px 0px 12.6px 0px rgba(27, 255, 63, 0.2)",
        btnShadow: "rgb(16, 18, 24, 1) 0px 3px 4px 0px",
        btnShadow2: "3.09px, 29.36px, 47.91px 0px rgba(0, 0, 0, 0.5)",
      },

      keyframes: {

        heroOneText: {
          "0%": { transform: "translateY(220%)" },
          "5%": { transform: "translateY(0%)" },
          "10%": { transform: "translateY(0%)" },
          "15%": { transform: "translateY(0%)" },
          "20%": { transform: "translateY(0%)" },
          "25%": { transform: "translateY(0%)" },
          "30%": { transform: "translateY(0%)" },
          "35%": { transform: "translateY(0%)" },
          "40%": { transform: "translateY(0%)" },
          "45%": { transform: "translateY(-0%)" },
          "47.5%": { transform: "translateY(220%)" },
        },

        heroTwoText: {
          "50%": { transform: "translateY(220%)" },
          "55%": { transform: "translateY(0%)" },
          "60%": { transform: "translateY(0%)" },
          "65%": { transform: "translateY(0%)" },
          "70%": { transform: "translateY(0%)" },
          "75%": { transform: "translateY(0%)" },
          "80%": { transform: "translateY(0%)" },
          "85%": { transform: "translateY(0%)" },
          "90%": { transform: "translateY(0%)" },
          "95%": { transform: "translateY(-0%)" },
          "100%": { transform: "translateY(220%)" },
        },

      },

      animation: {
        heroOneText: "heroOneText 5s linear 0s infinite ",
        heroTwoText: "heroTwoText 5s linear 0s infinite",
      },

      animation: {
        shimmer: "shimmer 2s infinite linear",
        gradientMove: "gradientMove 3s infinite linear",
        springyBounce: "springyBounce 1.2s infinite ease-out",
        coinFlip: "coinFlip 1.2s infinite ease-in-out"

      },
      keyframes: {
        shimmer: {
          "0%": { opacity: 0.7 },
          "50%": { opacity: 1 },
          "100%": { opacity: 0.7 },
        },
        gradientMove: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        springyBounce: {
          "0%, 100%": { transform: "scale(1)", opacity: 1 },
          "30%": { transform: "scale(1.15)", opacity: 0.9 },
          "60%": { transform: "scale(0.95)", opacity: 1 },
          "80%": { transform: "scale(1.05)" },
        },
        coinFlip: {
          "0%": { transform: "rotateY(0deg)" },
          "50%": { transform: "rotateY(180deg)" },
          "100%": { transform: "rotateY(360deg)" },
        },
      },

      screens: {
        lxl: "1921px",
        // => @media (min-width: 1921px) { ... }

        xlg: "1400px",
        // => @media (min-width: 992px) { ... }

        nlg: "992px",
        // => @media (min-width: 992px) { ... }

        mxs: "576px",
        // => @media (min-width: 576px) { ... }

        xxm: "420px",
        // => @media (min-width: 420px) { ... }

        xxs: "400px",
        // => @media (min-width: 400px) { ... }
      },
    },
  },
  plugins: [],
};
