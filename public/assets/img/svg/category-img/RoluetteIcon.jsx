import React from 'react';

function RoluetteIcon(props) {
    return (
        <svg 
        width="50" 
        height="49" 
        viewBox="0 0 50 49" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg" 
        {...props}
        >
            <g clipPath="url(#clip0_2001_892)">
                <path d="M40.4035 36.3382L39.2876 37.9507C37.7029 40.2408 34.5743 41.0743 31.5621 40.0027C30.4665 39.6125 29.2391 39.1363 27.9192 38.5587C22.2002 36.0532 14.7446 31.6503 8.76722 24.2263C6.89002 21.8944 6.55435 18.9013 7.92235 16.6732L8.32642 16.0158L9.12695 14.7124L40.4035 36.3382Z" fill="#F88610" />
                <path opacity="0.11" d="M9.1257 14.7124L40.4035 36.3382L39.2876 37.9507C37.703 40.2408 34.5743 41.0743 31.5622 40.0027C30.4665 39.6125 29.2391 39.1363 27.9192 38.5587C27.9332 38.5473 27.9484 38.5359 27.9623 38.5232C29.0554 37.6175 28.8135 35.8771 27.5139 35.3046C12.7648 28.8066 9.08516 18.8063 8.3239 16.0145L9.1257 14.7124Z" fill="black" />
                <path d="M41.3854 35.4422C43.7398 32.0408 38.5419 24.3641 29.7754 18.2959C21.0089 12.2276 11.9936 10.0656 9.63916 13.467C7.28469 16.8683 12.4826 24.545 21.2491 30.6133C30.0156 36.6815 39.0309 38.8435 41.3854 35.4422Z" fill="#FFAD3C" />
                <path d="M42.1841 33.9476C44.5386 30.5462 39.3406 22.8696 30.5742 16.8013C21.8077 10.733 12.7924 8.57105 10.4379 11.9724C8.08348 15.3738 13.2814 23.0504 22.0479 29.1187C30.8144 35.187 39.8297 37.3489 42.1841 33.9476Z" fill="#FFCE85" />
                <path d="M39.6562 32.1632L39.6536 32.1657C38.8138 33.3804 36.7365 33.5806 34.0094 32.927C30.9149 32.1847 26.9832 30.3442 23.0679 27.6374C19.159 24.9343 16.0506 21.9108 14.2633 19.2812C12.6825 16.9556 12.1328 14.9365 12.9751 13.7205L12.9776 13.718C14.7738 11.1302 22.1964 13.1581 29.5608 18.2501C36.9252 23.3421 41.4434 29.569 39.6562 32.1632Z" fill="#FFAD3C" />
                <path d="M39.6561 32.1633C39.5826 30.4115 37.4635 26.856 29.2796 20.0514C21.0957 13.2469 12.9776 13.7168 12.9776 13.7168C14.7737 11.129 22.1964 13.157 29.5608 18.249C36.9252 23.341 41.4434 29.5692 39.6561 32.1633Z" fill="#F88610" />
                <path d="M35.7713 31.9594C35.4103 32.4813 34.8036 32.7992 34.0094 32.9271C33.8282 32.8841 33.6446 32.8372 33.4584 32.7853C32.6477 32.5649 31.7864 32.2735 30.8883 31.9163C30.0143 31.568 29.1048 31.1576 28.1726 30.6877C27.6127 30.4052 27.0452 30.1012 26.4714 29.7769C25.6544 29.3146 24.8273 28.8092 23.9976 28.2645C23.6873 28.0606 23.377 27.8516 23.0679 27.6375C22.9754 27.5729 22.8817 27.5083 22.7905 27.4437C22.2002 27.0295 21.6302 26.609 21.0805 26.1834C20.5954 25.8085 20.1267 25.4297 19.6758 25.0497C18.7435 24.2644 17.886 23.4715 17.1133 22.6861C16.4812 22.0439 15.9062 21.4068 15.3944 20.7836C14.9739 20.2719 14.5964 19.7703 14.2646 19.2813C14.186 18.9013 14.1645 18.5467 14.2038 18.2224C14.2456 17.8715 14.3583 17.5561 14.547 17.2838C14.6395 17.1495 14.7484 17.0292 14.8726 16.9228C15.5908 16.3009 16.8207 16.1223 18.3648 16.3401C19.0741 16.4402 19.8506 16.6239 20.6752 16.8861C21.6771 17.2053 22.7487 17.641 23.8558 18.1844C24.4144 18.4593 24.9818 18.7607 25.5556 19.0888C26.2536 19.4891 26.9578 19.9273 27.6621 20.4049C27.7558 20.4682 27.8508 20.5328 27.9446 20.5987C28.2549 20.8127 28.5576 21.0293 28.854 21.2497C29.2999 21.5803 29.7293 21.9147 30.1422 22.2529C30.6071 22.6342 31.0504 23.0205 31.4697 23.4069C32.0182 23.9123 32.5261 24.4215 32.9884 24.9256C33.566 25.5539 34.0727 26.1758 34.5021 26.78C35.2596 27.8453 35.7751 28.8535 36.0031 29.7377C36.169 30.3849 36.1817 30.9651 36.0221 31.4553C35.9613 31.6351 35.879 31.8049 35.7713 31.9594Z" fill="#534741" />
                <path d="M28.8515 21.2471L23.9989 28.2644C23.6885 28.0605 23.3782 27.8515 23.0691 27.6374C22.9767 27.5728 22.8829 27.5082 22.7917 27.4436L27.6595 20.4022C27.7533 20.4656 27.8483 20.5302 27.942 20.596C28.2511 20.8101 28.5551 21.028 28.8515 21.2471Z" fill="#AC1736" />
                <path d="M31.4671 23.4055L29.1593 24.0224L27.3695 24.5012L26.8995 24.6266L26.8109 24.6506L26.4283 24.7532L26.1813 24.8191L25.4201 25.023L25.4023 25.0281L25.3605 25.0395L24.2509 25.3359L21.083 26.1833C20.5979 25.8084 20.1292 25.4296 19.6783 25.0496L23.1793 24.1136L23.6923 23.9768L24.3624 23.7982L25.0806 23.6056L25.5315 23.4853L25.6443 23.4549L25.9027 23.3865L26.1598 23.3181L26.4917 23.2294L27.7102 22.9039L30.1422 22.2528C30.6045 22.6328 31.0479 23.0179 31.4671 23.4055Z" fill="#AC1736" />
                <path d="M17.1133 22.6859L23.1768 24.1122L24.3852 24.3972L24.8247 24.5011L24.9489 24.5302L25.3061 24.6138L26.1788 24.819L26.3523 24.8595L26.8856 24.9849L27.258 25.0723L28.9819 25.4777L34.5008 26.776C34.0714 26.1718 33.5635 25.5499 32.9871 24.9216L29.1593 24.021L27.2441 23.5701L26.5284 23.4016L26.1573 23.3142L25.7722 23.2243L25.7177 23.2116L24.9501 23.0305L24.9451 23.0292L23.4643 22.6809L20.3496 21.9475L15.3957 20.7821L17.1133 22.6859Z" fill="#AC1736" />
                <path d="M28.1738 30.6875C27.6139 30.405 27.0464 30.101 26.4726 29.7768L25.6062 25.94L25.42 25.1142L25.401 25.0268L25.3972 25.009L25.3073 24.6126L25.1692 24.0033L25.0793 23.603L24.9501 23.033V23.0305L24.6892 21.8766L23.8544 18.1817C24.413 18.4566 24.9805 18.758 25.5543 19.0861L26.2966 22.3744L26.4891 23.2268L26.5284 23.4029L26.6816 24.0844L26.7323 24.3098L26.7665 24.4631L26.8083 24.6493L26.8843 24.9837L27.1275 26.0591L27.4974 27.6994L28.1738 30.6875Z" fill="#AC1736" />
                <path d="M36.0208 31.454L28.93 27.1536L27.1288 26.0605L25.9977 25.374L25.4175 25.0231L25.396 25.0104L25.2782 24.9382L24.7462 24.6165L24.3852 24.3974L23.6898 23.9756L20.3483 21.9489L14.2025 18.2224C14.2443 17.8715 14.357 17.5561 14.5457 17.2838C14.6382 17.1495 14.7471 17.0292 14.8713 16.9228L22.6017 21.6107L24.9451 23.0319L24.9501 23.0344L25.5784 23.4157L25.643 23.455L26.2548 23.8261L26.6829 24.0858L26.8286 24.1744L27.3695 24.5025L28.9819 25.4804L36.0018 29.7376C36.1677 30.3836 36.1804 30.9638 36.0208 31.454Z" fill="#AC1736" />
                <path d="M33.4583 32.7864C32.6477 32.566 31.7863 32.2747 30.8883 31.9175L27.4987 27.7007L25.8267 25.6209L25.4201 25.1142L25.3593 25.0382L25.2782 24.9381L24.9501 24.5303L24.8729 24.434L24.3599 23.7969L23.4643 22.6822L22.6017 21.6093L18.3635 16.3387C19.0728 16.4388 19.8493 16.6225 20.6739 16.8847L24.6892 21.8779L25.738 23.1825L25.7722 23.2256L25.9001 23.3852L26.2535 23.8247L26.6918 24.3707L26.7678 24.4644L26.8983 24.6278L27.2567 25.0737L28.9287 27.1535L33.4583 32.7864Z" fill="#AC1736" />
                <path d="M31.4127 27.892C31.2049 28.1922 30.6805 28.2429 29.9547 28.0833C29.7787 28.0453 29.5912 27.9933 29.3936 27.9313C28.3853 27.6121 27.1098 26.9876 25.7785 26.1301C25.6392 26.0401 25.4999 25.9489 25.3593 25.8539C25.2845 25.8045 25.2111 25.7526 25.1363 25.7019C23.6759 24.6924 22.4675 23.6145 21.6961 22.6898C21.5542 22.5201 21.4275 22.3554 21.3161 22.1971C20.8271 21.5004 20.646 20.9304 20.8715 20.6036C21.3566 19.9019 23.5631 20.5618 26.0547 22.0818C26.3055 22.2351 26.5575 22.3959 26.8121 22.5657C26.9236 22.6404 27.0351 22.7164 27.1478 22.7937C27.2225 22.8456 27.296 22.8963 27.3695 22.9482C27.5075 23.0457 27.6431 23.1445 27.7761 23.2433C27.9293 23.3573 28.0788 23.4713 28.2245 23.5853C28.4563 23.7665 28.6779 23.9476 28.8907 24.1275C30.773 25.7298 31.8471 27.2637 31.4127 27.892Z" fill="#FFAD3C" />
                <path d="M26.8134 22.5657L21.6973 22.6899C21.5555 22.5201 21.4288 22.3555 21.3173 22.1971L26.0547 22.0819C26.3055 22.2351 26.5588 22.396 26.8134 22.5657Z" fill="#F78511" />
                <path d="M29.9547 28.0833C29.7786 28.0453 29.5912 27.9934 29.3936 27.9313L28.2244 23.5841C28.4562 23.7652 28.6779 23.9464 28.8907 24.1262L29.9547 28.0833Z" fill="#F78511" />
                <path d="M27.3695 22.9484C27.5076 23.0459 27.6431 23.1447 27.7761 23.2435L25.7798 26.1315C25.6405 26.0416 25.5012 25.9504 25.3606 25.8554L27.3695 22.9484Z" fill="#F78511" />
                <path d="M30.9567 23.8731C30.9567 23.8731 27.1858 23.9731 24.7196 19.5601L26.1991 19.0154L26.2168 19.0091C27.6178 18.4923 28.868 17.6335 29.8509 16.5087L30.0118 16.4023L30.7984 15.8791L31.1556 15.6423L32.179 16.3503L32.1157 16.6379L31.87 17.7538C31.1872 19.0927 30.8541 20.581 30.901 22.0833L30.9567 23.8731Z" fill="#FFCF13" />
                <path d="M28.6247 25.3006C28.3663 25.1828 28.0788 25.0448 27.7735 24.8839C26.6829 24.3114 25.3529 23.4602 24.2459 22.2961C23.7683 21.7945 23.7075 21.0256 24.1015 20.4556L24.7209 19.5601C24.7209 19.5601 27.2352 23.3892 30.9579 23.8731L30.5121 24.6787C30.1409 25.3475 29.3189 25.6173 28.6247 25.3006Z" fill="#FFCF13" />
                <path opacity="0.33" d="M28.6248 25.3006C28.3664 25.1828 28.0789 25.0448 27.7736 24.8839C27.7825 24.8814 27.7926 24.8788 27.8015 24.875C28.5919 24.628 28.6881 23.5463 27.956 23.1587C25.5443 21.8832 24.6867 20.7533 24.3903 20.0364L24.7197 19.5601C24.7197 19.5601 27.234 23.3892 30.9567 23.8731L30.5109 24.6787C30.141 25.3475 29.3189 25.6173 28.6248 25.3006Z" fill="#F7850E" />
                <path opacity="0.22" d="M30.9567 23.8731C30.9567 23.8731 27.1858 23.9732 24.7196 19.5601L26.1991 19.0155C26.4195 19.3309 28.2701 21.8452 30.1207 20.0415C31.6648 18.5354 30.4906 17.2459 30.013 16.4011L30.7996 15.8779C31.2126 16.0705 31.6952 16.3618 32.117 16.6354L31.8712 17.7513C31.1885 19.0902 30.8554 20.5785 30.9022 22.0808L30.9567 23.8731Z" fill="#F7850E" />
                <path d="M29.861 13.8334L34.1791 16.819C34.1778 16.819 30.184 18.5277 29.861 13.8334Z" fill="#FFCF13" />
                <path d="M34.1778 16.8189C34.0752 16.9683 33.433 16.7125 32.611 16.2223C32.3462 16.0652 32.0625 15.8828 31.7724 15.6827C31.4887 15.4864 31.224 15.29 30.9884 15.1025C30.2296 14.4996 29.7572 13.9841 29.8598 13.8333C29.9522 13.7003 30.4728 13.8891 31.1682 14.2792C31.5026 14.4667 31.8776 14.701 32.2652 14.9695C32.6591 15.2419 33.0163 15.5155 33.314 15.765C33.9156 16.273 34.269 16.6871 34.1778 16.8189Z" fill="#F7850E" />
                <path d="M33.3633 15.6992C33.3481 15.722 33.3317 15.7436 33.3139 15.7651C33.1303 15.9982 32.8807 16.1527 32.6109 16.2224C32.3462 16.0653 32.0625 15.8829 31.7724 15.6828C31.5292 15.5143 31.2999 15.3458 31.0897 15.1824C31.0555 15.1558 31.0213 15.1292 30.9883 15.1026C30.9567 14.824 31.0137 14.5352 31.1682 14.2806C31.1821 14.2578 31.1961 14.235 31.2113 14.2134C31.6217 13.6194 32.4361 13.4712 33.0302 13.8816C33.1455 13.9614 33.2443 14.0564 33.3253 14.1615C33.6623 14.5998 33.6939 15.2204 33.3633 15.6992Z" fill="#FFD74A" />
                <path opacity="0.22" d="M33.3633 15.6994C33.3481 15.7222 33.3316 15.7437 33.3139 15.7652C33.1302 15.9983 32.8807 16.1528 32.6109 16.2225C32.3461 16.0654 32.0624 15.883 31.7723 15.6829C31.5291 15.5144 31.2999 15.346 31.0896 15.1826C31.481 15.3434 32.7249 15.7095 33.3253 14.1616C33.6622 14.5999 33.6939 15.2206 33.3633 15.6994Z" fill="#F7850E" />
                <path d="M32.044 14.1442C32.2016 14.0806 32.3035 13.965 32.2716 13.8858C32.2397 13.8067 32.086 13.7941 31.9285 13.8577C31.7709 13.9213 31.669 14.037 31.7009 14.1161C31.7328 14.1952 31.8865 14.2078 32.044 14.1442Z" fill="#FFFAEE" />
                <path opacity="0.55" d="M27.0642 19.6119C26.9008 19.1965 27.9306 18.1363 28.5754 18.3567C28.7552 18.4175 28.911 18.5809 28.9503 18.7633C29.044 19.2015 28.4677 19.7639 27.8622 19.8374C27.6178 19.8691 27.1554 19.845 27.0642 19.6119Z" fill="#FFFAEE" />
            </g>
            <defs>
                <clipPath id="clip0_2001_892">
                    <rect width="38" height="38" fill="white" transform="translate(5.92859 5.5)" />
                </clipPath>
            </defs>
        </svg>

    );
}

export default RoluetteIcon;
