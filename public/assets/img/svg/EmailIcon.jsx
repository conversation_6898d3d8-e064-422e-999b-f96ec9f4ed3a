import React from 'react';

function EmailIcon(props) {
    return (
        <svg 
        width="22" 
        height="22" 
        viewBox="0 0 22 22" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        {...props}
        >
            <g clipPath="url(#clip0_404_2483)">
                <path d="M12.8391 13.479C12.2917 13.8439 11.656 14.0368 11.0005 14.0368C10.345 14.0368 9.70928 13.8439 9.16194 13.479L0.146969 7.46883C0.0969531 7.43548 0.0482266 7.40072 0.000488281 7.36497V17.2133C0.000488281 18.3424 0.916797 19.2385 2.02573 19.2385H19.9752C21.1043 19.2385 22.0004 18.3222 22.0004 17.2133V7.36493C21.9526 7.40077 21.9038 7.43561 21.8537 7.469L12.8391 13.479Z" fill="#DFD3E5" />
                <path d="M0.862012 6.39623L9.87698 12.4064C10.2182 12.634 10.6093 12.7477 11.0004 12.7477C11.3916 12.7477 11.7827 12.6339 12.124 12.4064L21.139 6.39623C21.6784 6.03679 22.0005 5.43523 22.0005 4.78597C22.0005 3.6696 21.0923 2.76141 19.9759 2.76141H2.02505C0.908719 2.76146 0.000488281 3.66964 0.000488281 4.78705C0.000488281 5.43523 0.322582 6.03679 0.862012 6.39623Z" fill="#DFD3E5" />
            </g>
            <defs>
                <clipPath id="clip0_404_2483">
                    <rect width="22" height="22" fill="white" transform="translate(0.000244141)" />
                </clipPath>
            </defs>
        </svg>
    );
}

export default EmailIcon;
