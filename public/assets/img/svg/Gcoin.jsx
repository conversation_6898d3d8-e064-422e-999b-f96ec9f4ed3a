import React from "react";

const Gcoin = (props) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 38 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_1367_13760)">
        <rect x="4" y="0.5" width="30" height="30" rx="15" fill="#FFC048" />
        <path
          d="M23.0559 15.212C23.2692 15.212 23.4452 15.2867 23.5839 15.436C23.7332 15.5853 23.8079 15.7667 23.8079 15.98V19.58C23.8079 19.868 23.6799 20.092 23.4239 20.252C22.9332 20.572 22.3946 20.8227 21.8079 21.004C21.2212 21.1747 20.6186 21.26 19.9999 21.26C18.9332 21.26 17.9519 21.004 17.0559 20.492C16.1706 19.98 15.4719 19.2867 14.9599 18.412C14.4479 17.5267 14.1919 16.556 14.1919 15.5C14.1919 14.444 14.4479 13.4787 14.9599 12.604C15.4719 11.7187 16.1706 11.02 17.0559 10.508C17.9519 9.99599 18.9332 9.73999 19.9999 9.73999C20.5332 9.73999 21.0452 9.79866 21.5359 9.91599C22.0372 10.0333 22.4852 10.2093 22.8799 10.444C23.0079 10.5187 23.1039 10.6093 23.1679 10.716C23.2319 10.8227 23.2639 10.94 23.2639 11.068C23.2639 11.2813 23.1892 11.468 23.0399 11.628C22.9012 11.7773 22.7306 11.852 22.5279 11.852C22.3679 11.852 22.2292 11.8147 22.1119 11.74C21.4186 11.42 20.7146 11.26 19.9999 11.26C19.2319 11.26 18.5279 11.4467 17.8879 11.82C17.2586 12.1933 16.7572 12.7053 16.3839 13.356C16.0212 14.0067 15.8399 14.7213 15.8399 15.5C15.8399 16.2787 16.0212 16.9933 16.3839 17.644C16.7572 18.284 17.2586 18.796 17.8879 19.18C18.5279 19.5533 19.2319 19.74 19.9999 19.74C20.3626 19.74 20.7412 19.6973 21.1359 19.612C21.5412 19.5267 21.8879 19.4147 22.1759 19.276V16.732H20.2399C20.0266 16.732 19.8452 16.6627 19.6959 16.524C19.5466 16.3747 19.4719 16.1933 19.4719 15.98C19.4719 15.756 19.5466 15.5747 19.6959 15.436C19.8452 15.2867 20.0266 15.212 20.2399 15.212H23.0559Z"
          fill="white"
        />
        <rect
          x="8.25"
          y="4.75"
          width="21.5"
          height="21.5"
          rx="10.75"
          stroke="white"
          strokeWidth="0.5"
        />
        <rect x="6.5" y="3" width="35" height="35" rx="12.5" stroke="white" />
      </g>
      <defs>
        <filter
          id="filter0_d_1367_13760"
          x="0"
          y="0.5"
          width="38"
          height="38"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_1367_13760"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_1367_13760"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default Gcoin;
