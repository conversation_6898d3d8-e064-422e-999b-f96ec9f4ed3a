import React from "react";

const Scoin = (props) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 38 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_1367_13770)">
        <rect x="4" y="0.5" width="30" height="30" rx="15" fill="#33D9B2" />
        <path
          d="M19.1042 21.26C18.3255 21.26 17.6375 21.1533 17.0402 20.94C16.4429 20.716 15.8722 20.3587 15.3282 19.868C15.1042 19.676 14.9922 19.4573 14.9922 19.212C14.9922 19.02 15.0669 18.8493 15.2162 18.7C15.3655 18.54 15.5415 18.46 15.7442 18.46C15.9255 18.46 16.0802 18.5187 16.2082 18.636C16.6349 19.0307 17.0722 19.324 17.5202 19.516C17.9789 19.6973 18.4962 19.788 19.0722 19.788C19.7442 19.788 20.3095 19.6333 20.7682 19.324C21.2375 19.0147 21.4722 18.6253 21.4722 18.156C21.4615 17.6013 21.2269 17.1747 20.7682 16.876C20.3202 16.5667 19.6322 16.3107 18.7042 16.108C17.6055 15.884 16.7575 15.516 16.1602 15.004C15.5735 14.492 15.2802 13.7827 15.2802 12.876C15.2802 12.2467 15.4455 11.6973 15.7762 11.228C16.1069 10.748 16.5655 10.38 17.1522 10.124C17.7389 9.86799 18.4002 9.73999 19.1362 9.73999C19.7975 9.73999 20.4215 9.84666 21.0082 10.06C21.5949 10.2733 22.0695 10.556 22.4322 10.908C22.6775 11.1213 22.8002 11.3507 22.8002 11.596C22.8002 11.788 22.7255 11.9587 22.5762 12.108C22.4375 12.2573 22.2669 12.332 22.0642 12.332C21.9149 12.332 21.7922 12.2893 21.6962 12.204C21.4189 11.9267 21.0349 11.692 20.5442 11.5C20.0535 11.308 19.5842 11.212 19.1362 11.212C18.4215 11.212 17.8509 11.3613 17.4242 11.66C17.0082 11.948 16.8002 12.332 16.8002 12.812C16.8002 13.3347 17.0082 13.7347 17.4242 14.012C17.8509 14.2893 18.4802 14.524 19.3122 14.716C20.1442 14.8973 20.8215 15.1107 21.3442 15.356C21.8775 15.6013 22.2882 15.9427 22.5762 16.38C22.8642 16.8173 23.0082 17.388 23.0082 18.092C23.0082 18.7107 22.8322 19.26 22.4802 19.74C22.1389 20.22 21.6695 20.5933 21.0722 20.86C20.4749 21.1267 19.8189 21.26 19.1042 21.26Z"
          fill="white"
        />
        <rect
          x="8.25"
          y="4.75"
          width="21.5"
          height="21.5"
          rx="10.75"
          stroke="white"
          strokeWidth="0.5"
        />
        <rect x="6.5" y="3" width="35" height="35" rx="12.5" stroke="white" />
      </g>
      <defs>
        <filter
          id="filter0_d_1367_13770"
          x="0"
          y="0.5"
          width="38"
          height="38"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_1367_13770"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_1367_13770"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default Scoin;
