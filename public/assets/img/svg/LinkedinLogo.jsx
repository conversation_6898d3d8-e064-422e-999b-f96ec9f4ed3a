import React from 'react';

function LinkedinLogo(props) {
    return (
        <svg 
        width="35" 
        height="35" 
        viewBox="0 0 25 25" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        {...props}
        >
            <path d="M20.75 2.75H4.25C3.85218 2.75 3.47064 2.90804 3.18934 3.18934C2.90804 3.47064 2.75 3.85218 2.75 4.25V20.75C2.75 21.1478 2.90804 21.5294 3.18934 21.8107C3.47064 22.092 3.85218 22.25 4.25 22.25H20.75C21.1478 22.25 21.5294 22.092 21.8107 21.8107C22.092 21.5294 22.25 21.1478 22.25 20.75V4.25C22.25 3.85218 22.092 3.47064 21.8107 3.18934C21.5294 2.90804 21.1478 2.75 20.75 2.75ZM9.5 17C9.5 17.1989 9.42098 17.3897 9.28033 17.5303C9.13968 17.671 8.94891 17.75 8.75 17.75C8.55109 17.75 8.36032 17.671 8.21967 17.5303C8.07902 17.3897 8 17.1989 8 17V11C8 10.8011 8.07902 10.6103 8.21967 10.4697C8.36032 10.329 8.55109 10.25 8.75 10.25C8.94891 10.25 9.13968 10.329 9.28033 10.4697C9.42098 10.6103 9.5 10.8011 9.5 11V17ZM8.75 9.5C8.5275 9.5 8.30999 9.43402 8.12498 9.3104C7.93998 9.18679 7.79578 9.01109 7.71064 8.80552C7.62549 8.59995 7.60321 8.37375 7.64662 8.15552C7.69002 7.93729 7.79717 7.73684 7.9545 7.5795C8.11184 7.42217 8.31229 7.31502 8.53052 7.27162C8.74875 7.22821 8.97495 7.25049 9.18052 7.33564C9.38609 7.42078 9.56179 7.56498 9.6854 7.74998C9.80902 7.93499 9.875 8.1525 9.875 8.375C9.875 8.67337 9.75647 8.95952 9.5455 9.1705C9.33452 9.38147 9.04837 9.5 8.75 9.5ZM17.75 17C17.75 17.1989 17.671 17.3897 17.5303 17.5303C17.3897 17.671 17.1989 17.75 17 17.75C16.8011 17.75 16.6103 17.671 16.4697 17.5303C16.329 17.3897 16.25 17.1989 16.25 17V13.625C16.25 13.1277 16.0525 12.6508 15.7008 12.2992C15.3492 11.9475 14.8723 11.75 14.375 11.75C13.8777 11.75 13.4008 11.9475 13.0492 12.2992C12.6975 12.6508 12.5 13.1277 12.5 13.625V17C12.5 17.1989 12.421 17.3897 12.2803 17.5303C12.1397 17.671 11.9489 17.75 11.75 17.75C11.5511 17.75 11.3603 17.671 11.2197 17.5303C11.079 17.3897 11 17.1989 11 17V11C11.0009 10.8163 11.0693 10.6393 11.192 10.5026C11.3148 10.366 11.4834 10.2791 11.666 10.2585C11.8485 10.2379 12.0323 10.2851 12.1824 10.391C12.3325 10.4969 12.4385 10.6542 12.4803 10.8331C12.9877 10.4889 13.5792 10.2895 14.1914 10.2561C14.8036 10.2228 15.4133 10.3568 15.955 10.6438C16.4968 10.9308 16.9501 11.36 17.2664 11.8852C17.5826 12.4105 17.7498 13.0119 17.75 13.625V17Z" fill="#59515E" />
        </svg>
    );
}

export default LinkedinLogo;
