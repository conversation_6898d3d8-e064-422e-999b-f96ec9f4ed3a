import { create } from "zustand";
import { persist } from "zustand/middleware";
import { useProtectAuth } from "./useProtectAuth";

const useUserStore = create(
  persist(
    (set) => ({
      userIp: null,
      setUserIp: (data) => set(() => ({ userIp: data })),
      isLoggedIn: false,
      setIsLoggedIn: (data) => set(() => ({ isLoggedIn: data })),
      user: {},
      setUser: (data) => set(() => ({ user: data })),
      selectedCoin: "GC",
      setSelectedCoin: (data) => set(() => ({ selectedCoin: data })),
      welcomeBonusPurchase: null,
      setWelcomeBonusPurchase: (data) => {
        set(() => ({ welcomeBonusPurchase: data }));
      },
      clearAllData:() =>{
        set(() => ({ user: {}, isLoggedIn: false, selectedCoin: "GC" }));
        useUserStore.persist.clearStorage(); 
      },
      logout: () => {
        set(() => ({ user: {}, isLoggedIn: false, selectedCoin: "GC" }));

        const { clearAuthData } = useProtectAuth.getState();
        clearAuthData();
        localStorage.removeItem("authData");
        useUserStore.persist.clearStorage(); 
      },
      forceLogout: () => {
        set(() => ({ user: {}, isLoggedIn: false, selectedCoin: "GC" }));
        useUserStore.persist.clearStorage(); 
      },
      // logout: () => set(() => ({ user: {}, isLoggedIn: false })),

      // setUserWallet: (data) => {
      //   set(() => ({
      //   }));
      // },
    }),
    {
      name: "useUserStore",
    }
  )
);

export default useUserStore;
