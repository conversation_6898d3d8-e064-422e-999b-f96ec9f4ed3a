import { z } from "zod";

export const signupSchema = z.object({
  firstName: z
    .string()
    .trim()
    .min(1, "First name is required")
    .regex(/^[A-Za-z]+$/, "First name can only include alphabets"),
  lastName: z
    .string()
    .trim()
    .min(1, "Last name is required")
    .regex(/^[A-Za-z]+$/, "Last name can only include alphabets"),
  email: z.string().trim().email("Invalid email format"),
  password: z
    .string()
    .trim()
    .min(8, "Password must be at least 8 characters long"),
});

export const loginUserSchema = z.object({
  email: z.string().trim().email("Invalid email format"),

  password: z
    .string()
    .trim()
    .min(8, "Password must be at least 8 characters long"),
});
