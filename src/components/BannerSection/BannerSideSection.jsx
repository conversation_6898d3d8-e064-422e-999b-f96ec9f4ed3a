"use client";

import Image from "next/image";
import React, { useEffect, useState, useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import bannerBg from "../../assets/images/bannerBg.svg";
import { getBanners } from "@/actions";

const BannerSlider = ({ lobbySliderData }) => {
  const [banners] = useState(lobbySliderData || []);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });
  const [loading, setLoading] = useState(false);



  /*   useEffect(() => {
    setLoading(true);
    const fetchBanners = async () => {
      try {
        const bannersData = await getBanners();
        setBanners(
          bannersData?.filter((banner) => banner.pageName == "lobbySlider") ||
            []
        );
      } catch (error) {
        console.error("Failed to fetch banners", error);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []); */

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    const autoScroll = setInterval(() => {
      scrollNext();
    }, 3000);
    return () => clearInterval(autoScroll);
  }, [scrollNext]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap()); // Set the selected slide index
  }, [emblaApi]);

  // Attach Embla event listeners
  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on("select", onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  return (
    <div className="relative w-full h-auto bg-cover bg-center  shadow-lg flex-col flex items-center bg-custom-gradient rounded-lg max-md:overflow-visible [&>div]:h-full">
      {/* <div className="embla w-full [&>div]:h-full">
        <div
          className="embla__viewport rounded-lg overflow-hidden [&>div]:h-full"
          ref={emblaRef}
        >
          <div className="embla__container flex">
            {banners && banners?.length > 0 ? (
              banners?.map((banner, index) => (
                <div
                  key={index}
                  className="embla__slide flex-[0_0_100%] relative flex items-center justify-center"
                >
                  <div
                    style={{
                      backgroundImage: `url('${banner?.desktopImageUrl}')`,
                    }}
                    className={` w-full h-full blur-[10px] absolute bg-cover bg-no-repeat bg-center flex items-center justify-center`}
                  ></div>
                  <Image
                    height={462}
                    width={632}
                    src={banner?.desktopImageUrl}
                    alt=""
                    className="h-auto w-full object-cover relative z-[1]"
                    priority
                  />
                  <div className="absolute text-white text-left p-8 left-0 top-1/2 -translate-y-1/2">
                    <h2 className="text-4xl font-bold mb-4">{banner?.name}</h2>
                    <p className="text-lg font-medium">{banner?.textThree}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="embla__slide flex-[0_0_100%] relative">
                <Image
                  height={462}
                  width={632}
                  src={bannerBg}
                  alt="Default Banner"
                  className="h-full w-full object-cover"
                  priority
                />
                <div className="absolute text-white text-left p-8 left-0 top-1/2 -translate-y-1/2">
                  <h2 className="text-4xl font-bold mb-4">
                    Welcome to Vegas Coins
                  </h2>
                  <p className="text-lg font-medium">
                    Explore our wide range of casino games and find your
                    favorite today!
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div> */}
      <h5 className=" text-lg md:text-2xl text-center font-bold mb-0 pb-[2px] p-4 text-white">
        Experience the Thrill of Vegas Coins!
      </h5>
      <video
        width="100%"
        style={{ height: "100%" }}
        controls
        autoplay
        preload="metadata"
        poster="/assets/img/video/vid-thumbnail.jpg"
      >
        <source src="/assets/img/video/lobby-vid.MP4" type="video/mp4" />
      </video>
      {/* <iframe width="100%" height="100%" src="/assets/img/video/lobby-vid.mp4" frameborder="0"></iframe> */}
    </div>
  );
};

export default React.memo(BannerSlider);
