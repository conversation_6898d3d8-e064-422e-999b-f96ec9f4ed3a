import { getBanners, getProviderListing } from "@/actions";
import BannerSection from "@/components/BannerSection";
import React from "react";
// import TestimonialSlider from "./testimonial";

import cardOne from "../../../public/assets/img/png/chinaslots.png";
import cardTwo from "../../../public/assets/img/png/egyptgold.png";
import cardThree from "../../../public/assets/img/png/goldrushslots.png";
import cardFour from "../../../public/assets/img/png/santaslots.png";
import OperationImg from "../../../public/assets/img/png/usa-operation.png";
import SupportImg from "../../../public/assets/img/png/support-img.png";
import PromoGift from "../../../public/assets/img/png/promo-gift.png";
import ProviderOne from "../../../public/assets/img/png/provider-1.png";
import ProviderTwo from "../../../public/assets/img/png/provider-2.png";
import ProviderThree from "../../../public/assets/img/png/provider-3.png";
import ProviderFour from "../../../public/assets/img/png/provider-4.png";
import LabelOne from "../../../public/assets/img/png/label-1.png";
import LabelTwo from "../../../public/assets/img/png/label-2.png";
import LabelThree from "../../../public/assets/img/png/label-3.png";
import DemoGame from "../../components/DemoGame";
import GetStarted from "../../components/GetStarted";

import Image from "next/image";


const NotLoggedInPage = async () => {
  const bannersData = await getBanners();

  let promotionalBanner = [];
  if (bannersData) {
    if (Array.isArray(bannersData)) {
      promotionalBanner =
        (bannersData || []).filter(
          (banner) => banner.pageName === "promotionPage"
        ) || [];
    } else {
      promotionalBanner = [];
    }
  }

  let lobbySlider = [];
  if (bannersData) {
    if (Array.isArray(bannersData)) {
      lobbySlider =
        (bannersData || [])?.filter(
          (banner) => banner?.pageName == "lobbySlider"
        ) || [];
    } else {
      lobbySlider = [];
    }
  }

  const getAllProviders = await getProviderListing();

  const providerOptions = [
    { value: null, label: "All" },
    ...(getAllProviders && Array.isArray(getAllProviders)
      ? getAllProviders
          .filter((provider) => provider.isActive)
          .map((provider) => ({
            value: provider.masterCasinoProviderId,
            label: provider.name,
          }))
      : []),
  ];

  const providers = [
    { id: 1, image: ProviderOne, altText: "Red Rake Gaming" },
    { id: 2, image: ProviderTwo, altText: "Hacksaw Gaming" },
    { id: 3, image: ProviderThree, altText: "3 Oaks Gaming" },
    { id: 4, image: ProviderFour, altText: "Mancala Gaming" },
  ];

  const demoGamesList = [
    { id: 1, imageUrl: cardOne, name: "chinatownslot" },
    { id: 2, imageUrl: cardTwo, name: "egyptslot" },
    { id: 3, imageUrl: cardThree, name: "goldrushslot" },
    { id: 4, imageUrl: cardFour, name: "christmasslot" },
  ];

  return (
    <div>
      <BannerSection
        promotionalBanner={promotionalBanner}
        lobbySlider={lobbySlider}
      />

      <div>
        <h3 className="text-center text-4xl max-md:text-xl font-bold capitalize text-white mb-7 pt-8">
          {" "}
          Why YOU Should Play At Vegas Coins Social Casino{" "}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-0 md:py-8">
          <div className="rounded-lg overflow-hidden">
            <div>
              <Image
                height={1000}
                width={1000}
                src={OperationImg}
                alt="card-game"
              />
            </div>
            <div className="border rounded-br-lg text-center rounded-bl-lg border-green-200 py-3 px-3 lg:px-16 flex justify-center text-white text-lg md:text-xl  lg:text-2xl font-extrabold items-center">
              U.S.A. Based Operations
            </div>
          </div>
          <div className="rounded-lg overflow-hidden">
            <div>
              <Image
                height={1000}
                width={1000}
                src={SupportImg}
                alt="card-game"
              />
            </div>
            <div className="border rounded-br-lg rounded-bl-lg text-center border-green-200 py-3 px-3 lg:px-14 flex justify-center text-white text-lg md:text-xl lg:text-2xl font-extrabold items-center">
              24/7 Fast & Nice Customer Support
            </div>
          </div>
          <div className="rounded-lg overflow-hidden">
            <div>
              <Image
                height={1000}
                width={1000}
                src={PromoGift}
                alt="card-game"
              />
            </div>
            <div className="border rounded-br-lg text-center rounded-bl-lg border-green-200 py-3 px-3 lg:px-14 flex justify-center text-white text-lg md:text-xl lg:text-2xl font-extrabold items-center">
              Awesome Promos Every Single Day
            </div>
          </div>
        </div>
      </div>
      <div>
        <h3 className="text-center text-4xl max-md:text-xl font-bold capitalize text-white mb-7 pt-6 bg-gra">
          {" "}
          Play Games From Industry Leading Providers
        </h3>
        <div className=" grid grid-cols-2 md:grid-cols-4 gap-6 py-2  md:py-8">
          {providers?.map((provider, index) => (
            <div
              key={provider?.id}
              className="bg-custom-gradient rounded-lg py-4 px-2 flex justify-center items-center"
            >
              <Image
                height={1000}
                width={1000}
                className="rounded-2xl max-w-36 w-full"
                src={provider?.image}
                alt={provider?.altText}
              />
            </div>
          ))}
        </div>
      </div>
      {/* <div className="grid mt-8 grid-cols-2 md:grid-cols-4 gap-6 "> */}

      {/* {demoGamesList?.map((game) => (
          <GameCard key={game?.id} game={game}  />
        ))} */}

      {/* <Image
          height={1000}
          width={1000}
          className="rounded-2xl cursor-pointer"
          src={cardOne}
          alt="card-game"
        /> */}
      {/* <Image
          height={1000}
          width={1000}
          className="rounded-2xl cursor-pointer"
          src={cardTwo}
          alt="card-game"
        />
        <Image
          height={1000}
          width={1000}
          className="rounded-2xl cursor-pointer"
          src={cardThree}
          alt="card-game"
        />
        <Image
          height={1000}
          width={1000}
          className="rounded-2xl cursor-pointer"
          src={cardFour}
          alt="card-game"
        /> */}
      {/* </div> */}

      <DemoGame demoGamesList={demoGamesList} />

      {/* <div className="mt-8">
        <h3 className="text-center text-4xl max-md:text-xl font-bold capitalize text-white mb-7">
          Get Started With Vegas Coins Social Casino Today!
        </h3>
        <div className="rounded-lg  bg-custom-gradient p-8">
          <div className=" grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
              <h4 className="font-bold text-xl">Register Account.</h4>
              <Image
                height={1000}
                width={1000}
                className="max-w-[260px]"
                src={LabelOne}
                alt="card-game"
              />
              <p className="font-medium text-lg">
                Create Your Account In Under 10 Seconds.
              </p>
            </div>
            <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
              <h4 className="font-bold text-xl">Verify Details.</h4>
              <Image
                height={1000}
                width={1000}
                className="max-w-[260px]"
                src={LabelTwo}
                alt="card-game"
              />
              <p className="font-medium text-lg">
                Verify Your Personal Details & KYC.
              </p>
            </div>

            <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
              <h4 className="font-bold text-xl">Register Account.</h4>
              <Image
                height={1000}
                width={1000}
                className="max-w-[260px]"
                src={LabelThree}
                alt="card-game"
              />
              <p className="font-medium text-lg">
                Claim Your Welcome & Daily Bonuses.
              </p>
            </div>
          </div>
          <div className="flex w-full justify-center mt-6">
            <PinkButton
              className=" max-w-[600px] w-full [&>.thirdSpan]:max-sm:flex [&>.thirdSpan]:max-sm:justify-center [&>.thirdSpan]:max-sm:max-w-full [&>.thirdSpan]:text-sm [&>.secondSpan]:max-sm:max-w-full [&>.firstSpan]:max-sm:max-w-full"
            >
              CREATE ACCOUNT
            </PinkButton>
          </div>
        </div>
      </div> */}
      <GetStarted />

      <div>
        <h3 className="text-center text-4xl max-md:text-xl font-bold capitalize text-white mb-7 pt-8">
          Trusted By Players Across The U.S.A
        </h3>
        <div className="rounded-lg grid grid-cols-1 md:grid-cols-2 gap-11 bg-custom-gradient p-8">
          {/* <TestimonialSlider/> */}
          <div className="px-6 rounded-2xl bg-blackOpacity-250 py-4 text-white">
            <p className="text-lg">
              "When I used the site, I was blown away by how smooth everything
              runs. Animal Slots like Tiger Gems are hilarious, and their
              customer support team fixed a login hiccup in minutes. 10/10 for
              fun and reliability!"
            </p>
            <h2 className="text-[30px] font-bold">Deklan K</h2>
            <h5 className="text-2xl font-semibold">Dallas, TX</h5>
          </div>
          <div className="px-6 rounded-2xl bg-blackOpacity-250 py-4 text-white">
            <p className="text-lg">
              "Vegas Coins is a gem! The game variety (Fishin’ Bear and CrashX
              are my faves) is insane.. Support answered my question about
              promos instantly. No complaints—this platform nails it."
            </p>
            <h2 className="text-[30px] font-bold">Davis P.</h2>
            <h5 className="text-2xl font-semibold">Irving, CA</h5>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotLoggedInPage;
