// src/components/GtmScript.jsx
'use client'; // This is only needed for this component

import { useEffect } from 'react';
import Script from 'next/script';
import useUserStore from '@/store/useUserStore';
import { getCookie } from '@/utils/clientCookieUtils';

export default function GtmScript() {
  const user = useUserStore((state) => state.user);

const userToken = getCookie('accessToken');
  useEffect(() => {
    
    window.dataLayer = window.dataLayer || [];

    if (userToken && user) {
      // Push user_id to dataLayer
      window.dataLayer.push({
        event: 'userData',
        user_id: user.id,
      });
    } else {
    
      window.dataLayer.push({
        event: 'userData',
        user_id: null,
      });
    }
  }, [userToken]);

  return (
    <>
      {/* <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': 
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], 
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src= 
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); 
            })(window,document,'script','dataLayer','GTM-WHKXRFKK');`,
        }}
      /> */}
      <noscript>
        <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-WHKXRFKK"
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        ></iframe>
      </noscript>
    </>
  );
}
