"use client";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { GreenButton, PinkSecondaryButton } from "../Common/Button";
import Social1 from "../../assets/images/Social1.svg";
import Social2 from "../../assets/images/Social2.svg";
import Social3 from "../../assets/images/Social3.svg";
import Cross from "@/assets/images/Cross";
import useModalsStore from "@/store/useModalsStore";
import { useFormState } from "react-dom";
import { appleLogin, facebookLogin, googleLogin, loginAction } from "@/actions";
import { toast } from 'react-toastify';
import Signup from "./Signup";
import useUserStore from "@/store/useUserStore";
import { useGoogleLogin } from "@react-oauth/google";
import ForgotPassword from "./ForgotPassword";
import OtpVerification from "./OtpVerification";
import { <PERSON>aEye, FaEyeSlash } from "react-icons/fa";
import AppleLogin from "react-apple-login";
import ForcedEmailModal from "./ForcedEmailModal";
import { useRouter } from "next/navigation";
import { useIP } from "@/utils/ipUtils";

const Login = () => {
  const [appleScriptLoaded, setAppleScriptLoaded] = useState(false);
  const { clearModals, openModal, setIsCloseNeed, closeModal } =
    useModalsStore();
  const [formState, formAction] = useFormState(loginAction);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isAgreed, setIsAgreed] = useState(false);
  const [loading, setLoading] = useState(false);
  const { setUser, user, setIsLoggedIn, setUserIp, userIp } = useUserStore();
  const [isFbSdkReady, setIsFbSdkReady] = useState(false);

  useIP(setUserIp);

  const handleSubmit = async (e) => {
    e.preventDefault();
    // logout()
    setLoading(true);
    const formData = new FormData(e.target);
    const formDataObject = Object.fromEntries(formData.entries());

    await formAction({
      ...formDataObject,
      userIp,
    });
  };

  useEffect(() => {
    if (formState?.data) {
      if (
        formState?.data?.user &&
        formState?.data?.user?.isEmailVerified == false
      ) {
        toast.error("Please verify your email");
      } else {
        toast.success(formState?.message);
      }
      setIsLoggedIn(true);
      setUser(formState?.data?.user);
      clearModals();
    } else if (formState?.apiErrors) {
      toast.error(formState?.apiErrors || "Failed to Login. Please try again.");
    }

    setLoading(false);
  }, [formState]);

  useEffect(() => {
    if (user && user?.isEmailVerified == false) {
      openModal(<OtpVerification email={user?.email} />);
    }
  }, [user]);

  const fetchData = async (data) => {
    const res = await googleLogin(data,userIp);
    if (res && res?.success) {
      setIsLoggedIn(true);
      setUser(res?.data?.user);
      clearModals();
    } else {
      toast.error(res?.message || "Failed to login with Google.");
    }
  };

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
        };
        fetchData(userData);
      }
    },
    onError: (errorResponse) => console.log(errorResponse),
  });

  useEffect(() => {
    // Dynamically load the Apple Sign-in SDK
    const script = document.createElement("script");
    script.src =
      "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js";
    script.async = true;
    document.body.appendChild(script);
    script.onload = () => setAppleScriptLoaded(true);

    // Cleanup script on component unmount
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const handleFacebookClick = () => {
    FB.login(
      function (response) {
        if (response && response.authResponse && response.authResponse.userID) {
          FB.api(
            `/${response.authResponse.userID}`,
            { fields: ["first_name", "last_name", "email"] },
            function (_response) {
              responseFacebook(_response);
            }
          );
        }
      },
      { scope: "public_profile,email" }
    );
  };

  const responseFacebook = (response) => {
    const userData = {
      firstName: response.first_name,
      lastName: response.last_name,
      userId: response.id,
      email: response.email,
      isSignup: true,
      isTermsAccepted: true,
      isForceEmail: false,
    };

    if (response && response.email) {
      handleFaceBookLogin(userData);
    } else {
      closeModal(<Login />);
      openModal(
        <ForcedEmailModal
          userData={userData}
          handleFaceBookLogin={handleFaceBookLogin}
          isForceEmail={true}
          userIp={userIp}
        />
      );
    }
  };

  const handleFaceBookLogin = async (userData) => {
    const res = await facebookLogin(userData, userIp);
    if (res?.data?.data?.user) {
      setIsLoggedIn(true);
      setUser(res?.data?.data?.user);
      clearModals();
      setLoading((prev) => !prev);
    } else {
      toast.error(res?.message || "Failed to login with Facebook..");
    }
  };

  const handleAppleLogin = async (response) => {
    if (response && !response.error) {
      const userData = {
        ...response,
        isSignup: false,
      };
      const result = await appleLogin(userData, userIp);

      if (result && result?.success) {
        setIsLoggedIn(true);
        setUser(result.data.user);
        clearModals();
        toast.success("Successfully logged in with Apple.");
      } else {
        toast.error(result?.message || "Failed to login with Apple.");
      }
    } else {
      toast.error("Apple login failed. Please try again.");
    }
  };

  const handleCloseModal = () => {
    if (!loading) {
      clearModals();
    }
  };
  return (
    <div className="flex items-center max-w-[766px] w-full rounded-lg justify-center bg-custom-gradient loggin">
      <div className="rounded-lg px-20 max-lg:px-10 max-md:px-5 max-sm:px-4 py-8 w-full shadow-lg relative ">
        <button
          onClick={handleCloseModal}
          className="absolute top-5 right-5 inline-block cursor-pointer hover:rotate-90 transition-transform duration-200"
        >
          <Cross />
        </button>
        <h2 className="text-center text-2xl max-md:text-xl font-bold capitalize text-white mb-7">
          Log In
        </h2>
        <form className="" onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-3.5">
            <div>
              <label
                className="block text-sm max-md:text-xs font-normal text-white mb-1"
                htmlFor="email"
              >
                Email
              </label>
              <input
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                name="email"
                id="email"
                type="email"
                className="bg-blackOpacity-250 placeholder-gray-1 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                placeholder="Email"
              />
              {formState?.zodErrors?.email?.[0] && (
                <p className="text-red-1 text-[12px] font-semibold mt-1">
                  {formState?.zodErrors?.email[0]}
                </p>
              )}
            </div>
            <div>
              <label
                className="block text-sm max-md:text-xs font-normal text-white mb-1"
                htmlFor="password"
              >
                Password
              </label>
              <div className="relative">
                <input
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  name="password"
                  id="password"
                  type={isPasswordVisible ? "text" : "password"}
                  className="bg-blackOpacity-250 placeholder-gray-1 text-white text-sm rounded-[5px] block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                  placeholder="Password"
                />
                <button
                  type="button"
                  onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white"
                >
                  {isPasswordVisible ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {formState?.zodErrors?.password?.[0] && (
                <p className="text-red-1 text-[12px] font-semibold mt-1">
                  {formState?.zodErrors?.password[0]}
                </p>
              )}
            </div>
          </div>
          <div className="flex justify-end mt-6 mb-7">
            <p
              onClick={() => openModal(<ForgotPassword />)}
              className="text-sm cursor-pointer  text-gray-1"
            >
              Forgot Password?
            </p>
          </div>

          <div className="flex items-center my-4 max-w-52 w-full mx-auto or-divider">
            <hr className="flex-grow border-gray-600" />
            <span className="mx-4 text-sm text-gray-300">OR</span>
            <hr className="flex-grow border-gray-600" />
          </div>
          <div className="grid grid-cols-3 gap-3">
            <button
              type="button"
              onClick={() => handleGoogleLogin()}
              className="bg-white px-4 py-2 rounded-md shadow-lg text-white flex items-center space-x-2 hover:bg-gray-600 hover:scale-105 transition-transform duration-300"
            >
              <Image
                width={24}
                height={24}
                src={Social2}
                alt="Facebook"
                className="w-6 h-6 mx-auto"
              />
            </button>
            <button
              type="button"
              onClick={handleFacebookClick}
              className="bg-blue-100 px-4 py-2 rounded-md shadow-lg text-white flex items-center space-x-2 hover:bg-blue-500 hover:scale-105 transition-transform duration-300"
            >
              <Image
                width={24}
                height={24}
                src={Social1}
                alt="Google"
                className="w-6 h-6 mx-auto"
              />
            </button>
            {appleScriptLoaded && (
              <AppleLogin
                clientId={process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}
                redirectURI={process.env.NEXT_PUBLIC_APPLE_REDIRECT_URL}
                usePopup={true}
                callback={handleAppleLogin}
                scope="email name"
                responseMode="query"
                render={(renderProps) => (
                  <button
                    type="button"
                    onClick={renderProps.onClick}
                    className="bg-gray-900 px-4 py-2 rounded-lg shadow-lg text-white flex items-center space-x-2 hover:bg-gray-600 hover:scale-105 transition-transform duration-300"
                  >
                    <Image
                      type="button"
                      width={24}
                      height={24}
                      src={Social3}
                      alt="Apple"
                      className="w-6 h-6 mx-auto"
                    />
                  </button>
                )}
              />
            )}
          </div>
          <div className="flex justify-center my-7">
            <GreenButton type="submit" className="w-full" disabled={loading}>
              {loading ? "Loading..." : "Login"}
            </GreenButton>
          </div>
        </form>
        <div className="text-center">
          <p className="text-gray-300 font-normal text-sm">
            Don't have an account?
            <button
              onClick={() => openModal(<Signup />)}
              className="text-white font-semibold ml-1 text-[16px]"
            >
              Register
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
