"use client";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { PinkButton } from "../Common/Button";
import Social1 from "../../assets/images/Social1.svg";
import Social2 from "../../assets/images/Social2.svg";
import Social3 from "../../assets/images/Social3.svg";
import Cross from "@/assets/images/Cross";
import useModalsStore from "@/store/useModalsStore";
import { useFormState } from "react-dom";
import {
  appleLogin,
  facebookLogin,
  getWelcomePurchaseBonus,
  googleLogin,
  signupAction,
} from "@/actions";
import { toast } from 'react-toastify';
import Login from "./Login";
import OtpVerification from "./OtpVerification";
import { useGoogleLogin } from "@react-oauth/google";
import useUserStore from "@/store/useUserStore";
import AppleLogin from "react-apple-login";
import { <PERSON>aEye, FaEyeSlash } from "react-icons/fa";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { getCookie } from "@/utils/clientCookieUtils";
import ForcedEmailModal from "./ForcedEmailModal";
import { useIP } from "@/utils/ipUtils";

const Signup = ({ className = "", from = "" }) => {
  const [appleScriptLoaded, setAppleScriptLoaded] = useState(false);
  const { clearModals, openModal, closeModal } = useModalsStore();
  const [formState, formAction] = useFormState(signupAction);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingGoogle, setIsLoadingGoogle] = useState(false);
  const [isLoadingFacebook, setIsLoadingFacebook] = useState(false);
  const [isLoadingApple, setIsLoadingApple] = useState(false);

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState({
    termsAndConditions: false,
    ageTerm: false,
  });
  const { setUser, setIsLoggedIn, userIp, setUserIp, setWelcomeBonusPurchase } =
    useUserStore();
  const searchParams = useSearchParams();

  useIP(setUserIp);

  let cachedIP = null;

  async function getIP() {
    if (cachedIP) {
      return cachedIP;
    }
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    cachedIP = data.ip; // Store just the IP string
    return cachedIP;
  }

  useEffect(() => {
    (async () => {
      try {
        const ip = await getIP();
        setUserIp(ip || "0.0.0.0"); // Update state with the IP
      } catch (error) {
        console.error("Failed to fetch IP:", error);
        setUserIp("0.0.0.0");
      }
     
    })();
  }, []);

  useEffect(() => {
    getIP()
   
  }, []);



  let pid = searchParams.get("pid");
  let cid = searchParams.get("cid");
  let click_id = searchParams.get("click_id");
  const sokul_id = getCookie("sokul_X73ysdf4s") || null;

  const trackSignUpEvent = (signUpData) => {
    try {
      // Validate if dataLayer exists
      if (typeof window === "undefined" || !window.dataLayer) {
        console.warn("DataLayer not available");
        return;
      }

      // Push the sign-up event
      window.dataLayer.push({
        event: "sign_up",
        user_id: signUpData.userId,
        affiliate_id: signUpData.affiliateId,
        click_id: signUpData.clickId,
      });
    } catch (error) {
      console.error("Error tracking sign-up event:", error);
    }
  };

  const updateTermsAccepted = (key, value) => {
    setIsTermsAccepted((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };


  // Add useEffect for initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 2500); // 2.5 seconds delay

    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isTermsAccepted.termsAndConditions && isTermsAccepted.ageTerm) {
      const new_sokul_id = getCookie("sokul_X73ysdf4s") || null;
      setIsLoading(true);
      const formData = new FormData(e.target);
      const formDataObject = Object.fromEntries(formData.entries());
      formDataObject["platform"] = navigator.platform;
      formDataObject["browser"] = navigator.appCodeName;
      formDataObject["isTermsAccepted"] =
        isTermsAccepted.termsAndConditions && isTermsAccepted.ageTerm;
      if (pid) formDataObject["affiliateId"] = pid;
      if (cid) formDataObject["affiliateCode"] = cid;
      if (click_id) formDataObject["affiliateCode"] = click_id;
      if (new_sokul_id) formDataObject["sokulId"] = new_sokul_id;
      await formAction({ ...formDataObject, userIp });
    } else {
      toast.error("All terms and conditions must be accepted.");
    }
  };

  useEffect(() => {
    if (formState?.data) {
      toast.success(formState?.message);
      clearModals();
      openModal(<OtpVerification email={formState?.data?.user?.email} />);
    } else if (formState?.apiErrors) {
      toast.error(formState?.apiErrors);
    }

    setIsLoading(false);
  }, [formState]);
  //  if (pid) data["affiliateId"] = pid;
  // if (cid) data["affiliateCode"] = cid;
  // if (click_id) data["affiliateCode"] = click_id;
  const fetchData = async (data) => {
    const new_sokul_id = getCookie("sokul_X73ysdf4s") || null;
    if (pid) data["affiliateId"] = pid;
    if (cid) data["affiliateCode"] = cid;
    if (click_id) data["affiliateCode"] = click_id;
    if (new_sokul_id) data["sokulId"] = new_sokul_id;
    
    // setLoader(true)
    const res = await googleLogin(data,userIp);
    if (res?.success) {
      toast.success("Successfully logged in");
      clearModals();
      setIsLoggedIn(true);
      const bonus = await getWelcomePurchaseBonus();
      setWelcomeBonusPurchase(bonus);
      setUser(res?.data?.user);
      if (res?.data?.user?.newGtmUser) {
        trackSignUpEvent({
          userId: res?.data?.user?.userId,
          affiliateId: pid,
          clickId: click_id || cid,
        });
      }
    } else {
      toast.error(res?.message || "Failed to login with google");
    }
    // setLoader(false)
    clearModals();
  };

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
        };
        fetchData(userData);
      }
    },
    onError: (errorResponse) => console.log(errorResponse),
  });

  useEffect(() => {
    // Dynamically load the Apple Sign-in SDK
    const script = document.createElement("script");
    script.src =
      "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js";
    script.async = true;
    document.body.appendChild(script);
    script.onload = () => setAppleScriptLoaded(true);

    // Cleanup script on component unmount
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const handleFacebookClick = () => {
    FB.login(
      function (response) {
        if (response && response.authResponse && response.authResponse.userID) {
          FB.api(
            `/${response.authResponse.userID}`,
            { fields: ["first_name", "last_name", "email"] },
            function (_response) {
              responseFacebook(_response);
            }
          );
        }
      },
      { scope: "public_profile,email" }
    );
  };

  const responseFacebook = (response) => {
    if (response) {
      const userData = {
        firstName: response.first_name,
        lastName: response.last_name,
        userId: response.id,
        email: response.email,
        isSignup: true,
        isTermsAccepted: true,
        isForceEmail: false,
      };
      const new_sokul_id = getCookie("sokul_X73ysdf4s") || null;
      if (new_sokul_id) userData["sokulId"] = new_sokul_id;
      if (response && response.email) {
        handleFaceBookLogin(userData);
      } else {
        closeModal(<Signup />);
        openModal(
          <ForcedEmailModal
            userData={userData}
            handleFaceBookLogin={handleFaceBookLogin}
            isForceEmail={true}
            userIp={userIp}
          />
        );
      }
    }
  };

  const handleFaceBookLogin = async (userData) => {
    const new_sokul_id = getCookie("sokul_X73ysdf4s") || null;
    if (pid) userData["affiliateId"] = pid;
    if (cid) userData["affiliateCode"] = cid;
    if (click_id) userData["affiliateCode"] = click_id;
    if (new_sokul_id) userData["sokulId"] = new_sokul_id;
    const res = await facebookLogin(userData, userIp);
    if (res?.data?.data?.user) {
      const bonus = await getWelcomePurchaseBonus();
      setWelcomeBonusPurchase(bonus);
      setIsLoggedIn(true);
      setUser(res?.data?.data?.user);
      clearModals();
      if (res?.data?.data?.user?.newGtmUser) {
        trackSignUpEvent({
          userId: res?.data?.data?.user?.userId,
          affiliateId: pid,
          clickId: click_id || cid,
        });
      }
    } else if (res?.success) {
      toast.error(res?.data?.errors[0]?.description);
    } else {
      toast.error("Something went wrong.");
    }
  };

  const handleAppleLogin = async (response) => {
    const new_sokul_id = getCookie("sokul_X73ysdf4s") || null;
    if (response && !response.error) {
      const userData = {
        ...response,
        isSignup: true,
      };
      if (pid) userData["affiliateId"] = pid;
      if (cid) userData["affiliateCode"] = cid;
      if (click_id) userData["affiliateCode"] = click_id;
      if (new_sokul_id) userData["sokulId"] = new_sokul_id;
      const result = await appleLogin(userData, userIp);
  
      if (result && result?.success) {
        const bonus = await getWelcomePurchaseBonus();
        setWelcomeBonusPurchase(bonus);
        setIsLoggedIn(true);
        setUser(result?.data?.user);
        clearModals();
        if (result?.data?.user?.newGtmUser) {
          trackSignUpEvent({
            userId: result?.data?.user?.userId,
            affiliateId: pid,
            clickId: click_id || cid,
          });
        }
        toast.success("Successfully logged in with Apple.");
      } else {
        toast.error(result?.message || "Failed to login with Apple.");
      }
    } else {
      toast.error("Apple login failed. Please try again.");
    }
  };

  // Add delay function
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  // Modify Google login handler
  const handleGoogleLoginWithDelay = async () => {
    setIsLoadingGoogle(true);
    await delay(3500); // 2.5 second delay
    handleGoogleLogin();
    setIsLoadingGoogle(false);
  };

  // Modify Facebook login handler
  const handleFacebookClickWithDelay = async () => {
    setIsLoadingFacebook(true);
    await delay(3500); // 2.5 second delay
    handleFacebookClick();
    setIsLoadingFacebook(false);
  };

  return (
    <div
      className={`flex items-center max-w-[766px] w-full justify-center bg-custom-gradient rounded-lg loggin ${className}`}
    >
      <div className="rounded-lg px-20 max-lg:px-10 max-md:px-5 max-sm:px-4 py-8 w-full shadow-lg relative modal-main-container">
        {from != "banner" && (
          <button
            onClick={() => clearModals()}
            className="absolute top-5 right-5 inline-block cursor-pointer"
          >
            <Cross />
          </button>
        )}
        <h2 className="text-center text-2xl max-md:text-xl font-bold capitalize text-white mb-7 pt-1">
          {from == "banner"
            ? "Vegas Coins - America's #1 Social Casino"
            : "Create A Vegas Coins Account"}
        </h2>
        
        {isInitialLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-white mb-4"></div>
            <p className="text-white text-lg">Loading...</p>
          </div>
        ) : (
          <form className="" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-x-3.5 gap-y-5 lobby-grid">
              <div>
                <label
                  className="block text-sm max-md:text-xs font-normal text-white mb-1"
                  htmlFor="firstName"
                >
                  First Name <span className="text-red-500">*</span>
                </label>
                <input
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  name="firstName"
                  id="firstName"
                  type="text"
                  placeholder="First Name"
                  className="bg-blackOpacity-250 h-11 placeholder-gray-1 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                />

                {formState?.zodErrors?.firstName?.[0] && (
                  <p className="text-red-1 text-[12px] font-semibold mt-1">
                    {formState?.zodErrors?.firstName[0]}
                  </p>
                )}
              </div>

              <div>
                <label
                  className="block text-sm max-md:text-xs font-normal text-white mb-1"
                  htmlFor="lastName"
                >
                  Last Name <span className="text-red-500">*</span>
                </label>
                <input
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  name="lastName"
                  id="lastName"
                  type="text"
                  className="bg-blackOpacity-250 h-11 placeholder-gray-1 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                  placeholder="Last Name"
                />
                {formState?.zodErrors?.lastName?.[0] && (
                  <p className="text-red-1 text-[12px] font-semibold mt-1">
                    {formState?.zodErrors?.lastName[0]}
                  </p>
                )}
              </div>
              <div>
                <label
                  className="block text-sm max-md:text-xs font-normal text-white mb-1"
                  htmlFor="email"
                >
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  name="email"
                  id="email"
                  type="email"
                  className="bg-blackOpacity-250 h-11 placeholder-gray-1 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                  placeholder="Email"
                />
                {formState?.zodErrors?.email?.[0] && (
                  <p className="text-red-1 text-[12px] font-semibold mt-1">
                    {formState?.zodErrors?.email[0]}
                  </p>
                )}
              </div>
              <div>
                <label
                  className="block text-sm max-md:text-xs font-normal text-white mb-1"
                  htmlFor="password"
                >
                  Password <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    name="password"
                    id="password"
                    type={isPasswordVisible ? "text" : "password"}
                    className="bg-blackOpacity-250 h-11 placeholder-gray-1 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                    placeholder="Password"
                  />
                  <button
                    type="button"
                    onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white"
                  >
                    {isPasswordVisible ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                {formState?.zodErrors?.password?.[0] && (
                  <p className="text-red-1 text-[12px] font-semibold mt-1">
                    {formState?.zodErrors?.password[0]}
                  </p>
                )}
              </div>
            </div>
            {/* <div className="relative mt-5">
              <input
                value={promocode}
                onChange={(e) => setPromoCode(e.target.value)}
                name="promocode"
                id="promocode"
                type="text"
                className="bg-blackOpacity-250 h-11 placeholder-gray-1 text-white text-sm rounded-md block w-full pl-4 pr-[90px] py-3border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none"
                placeholder="I have an promo code (optional)"
              />
              <div className="absolute top-[9px] right-3.5">
                <GrayButton>apply</GrayButton>
              </div>
            </div> */}
            <div className="flex items-start gap-2 pt-2.5 pb-1 max-sm:pb-2 mt-4">
              <input
                checked={isTermsAccepted.termsAndConditions}
                onChange={() =>
                  updateTermsAccepted(
                    "termsAndConditions",
                    !isTermsAccepted.termsAndConditions
                  )
                }
                type="checkbox"
                className="form-checkbox h-5 min-h-5 w-5 min-w-5 p-0 checked:bg-checkIcon checked:bg-contain checked:drop-shadow-checkboxShadow rounded appearance-none border-2 border-solid border-white bg-transparent cursor-pointer"
              />
              <p className="text-sm text-gray-1 ">
                {/* I agree with the Terms & Conditions, Privacy Policy and confirm I
                am 21+ years old. */}
                I accept and agree to be bound by the{" "}
                <Link href={"/VCTOS1.0.pdf"} className="text-white">
                  Terms & Conditions
                </Link>{" "}
                and{" "}
                <Link href={"/VCPP1.0.pdf"} className="text-white">
                  Privacy Policy.
                </Link>
              </p>
            </div>

            <div className="flex items-start gap-2  pb-1 max-sm:pb-2">
              <input
                checked={isTermsAccepted.ageTerm}
                onChange={() =>
                  updateTermsAccepted("ageTerm", !isTermsAccepted.ageTerm)
                }
                type="checkbox"
                className="form-checkbox h-5 min-h-5 w-5 min-w-5 p-0 checked:bg-checkIcon checked:bg-contain checked:drop-shadow-checkboxShadow rounded appearance-none border-2 border-solid border-white bg-transparent cursor-pointer"
              />
              <p className="text-sm text-gray-1 mb-[5px]">
                I am 18+ and understand the website is strictly for social casino
                gaming with no real money required.
              </p>
            </div>

            <PinkButton
              type="submit"
              className="w-full  register-lobby-btn hidden uppercase"
              disabled={isLoading}
            >
              <span className="uppercase">
                {isLoading ? "Loading..." : "create account"}
              </span>
            </PinkButton>

            <div className="flex items-center my-4 max-w-52 w-full mx-auto or-divider">
              <hr className="flex-grow border-gray-1000" />
              <span className="mx-4 text-sm text-gray-300">OR</span>
              <hr className="flex-grow border-gray-1000" />
            </div>
            <div className="grid grid-cols-3 gap-3">
              <button
                type="button"
                onClick={handleGoogleLoginWithDelay}
                disabled={isLoadingGoogle}
                className="bg-white px-4 py-2 rounded-md shadow-lg text-white flex items-center space-x-2 hover:bg-gray-600 disabled:opacity-50"
              >
                <Image
                  width={24}
                  height={24}
                  src={Social2}
                  alt="Facebook"
                  className="w-6 h-6 mx-auto"
                />
              </button>
              <button
                type="button"
                onClick={handleFacebookClickWithDelay}
                disabled={isLoadingFacebook}
                className="bg-blue-100 px-4 py-2 rounded-md shadow-lg text-white flex items-center space-x-2 hover:bg-blue-500 disabled:opacity-50"
              >
                <Image
                  width={24}
                  height={24}
                  src={Social1}
                  alt="Google"
                  className="w-6 h-6 mx-auto"
                />
              </button>
              {appleScriptLoaded && (
                <AppleLogin
                  clientId={process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}
                  redirectURI={process.env.NEXT_PUBLIC_APPLE_REDIRECT_URL}
                  usePopup={true}
                  callback={handleAppleLogin}
                  scope="email name"
                  responseMode="query"
                  render={(renderProps) => (
                    <button
                      onClick={renderProps.onClick}
                      className="bg-gray-900 px-4 py-2 rounded-lg shadow-lg text-white flex items-center space-x-2 hover:bg-gray-600"
                    >
                      <Image
                        type="button"
                        width={24}
                        height={24}
                        src={Social3}
                        alt="Apple"
                        className="w-6 h-6 mx-auto"
                      />
                    </button>
                  )}
                />
              )}
            </div>
            <div className="flex justify-center register-modal-btn">
              <PinkButton
                type="submit"
                className="w-full mt-7"
                disabled={isLoading}
              >
                {isLoading ? "Loading..." : "Register"}
              </PinkButton>
            </div>
          </form>
        )}
        
        {!isInitialLoading && (
          <div className="text-center mt-7 bottom-login">
            <p className="text-gray-300 font-normal text-sm">
              Already have an account?
              <button
                onClick={() => openModal(<Login />)}
                className="text-white font-bold ml-2.5 text-[16px]"
              >
                Login
              </button>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Signup;
