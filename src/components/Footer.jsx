import LogoGray from "../assets/images/logo/logo-gray.svg";
import { getCmsLinks } from "@/actions";
import Image from "next/image";
import Link from "next/link";
import AgeLogo from "../assets/images/social/age-icon.svg";
import TrustPilot from "../assets/images/social/trust-pilot.png";
import FacebookLogo from "../assets/images/social/facebook.svg";
import Telegram from "../assets/images/social/telegram.svg";
import FooterNumber from "../assets/images/footer-number.svg";
import Contact from "./Contact";
import { cookies } from "next/headers";

export default async function Footer() {
  const accessToken = cookies().get("accessToken");

  let cmsLinks = await getCmsLinks();
  let cmsLinksArray = cmsLinks?.data?.filter(
    (cmsLink) => cmsLink?.slug !== "VCSR1.0.pdf"
  );

  if (accessToken && accessToken?.value != "") {
    cmsLinksArray = cmsLinks?.data;
  }
  let footer_desctiption = cmsLinksArray?.find(
    (cmsLink, i) => cmsLink?.slug === "footer"
  );
  return (
    <div className="w-full bg-primary-100 p-4">
      <footer
        className="max-w-7xl w-full max-sm:pt-[30px
      ] pb-[90px] sm:py-10 mx-auto text-white bg-darkBlue"
      >
        <div className="grid grid-cols-1 md:grid-cols-12 gap-2 max-md:gap-6">
          <div className="flex gap-[11px] max-sm:gap-5 flex-col md:col-start-1 md:col-span-7 pr-14 max-md:pr-0 max-md:order-2">
            <div className="flex items-center justify-between w-full gap-x-4 max-sm:flex-col max-sm:gap-y-2.5">
              <Image src={LogoGray} alt="Vegas Coins Logo" className="h-10" />
              <div className="flex gap-4">
                <Link
                  href="https://www.facebook.com/VegasCoinsOfficial"
                  target="_blank"
                >
                  <Image
                    src={FacebookLogo}
                    alt="Facebook Logo"
                    width={24}
                    height={24}
                  />
                </Link>
                <Link href="https://t.me/vegascoins" target="_blank">
                  <Image
                    src={Telegram}
                    alt="X (Twitter) Logo"
                    width={24}
                    height={24}
                  />
                </Link>
              </div>
            </div>

            <div className="flex w-full flex-wrap gap-[10px] md:gap-10  mb-4 text-[13px]">
              {cmsLinksArray?.map((cmsLink, i) => {
                if (cmsLink?.isHidden === true) {
                  return null;
                }

                // Normalize the slug to remove any path before it
                const normalizedSlug = cmsLink?.slug?.startsWith("/")
                  ? cmsLink.slug
                  : `/${cmsLink.slug}`;

                return (
                  <Link
                    key={i}
                    href={normalizedSlug}
                    className="hover:text-green-500 transition underline"
                  >
                    {cmsLink?.title?.EN}
                  </Link>
                );
              })}
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html:
                  footer_desctiption?.content?.EN ||
                  ` <div className="text-[13px] text-pink-5  leading-1.5 mb-4 max-sm:text-center flex gap-1 flex-col">
              <p className="mb-[20px]">
                Vegas Coins offers social play only and does not involve any
                real money gambling. Vegas Coins is solely for entertainment
                purposes and no real money is required to access and use Vegas
                Coins or to play the games. Users must be 21+ to access our
                platform. © 2024 Vegas Coins Inc. All rights reserved.
              </p>
              <p className="mb-[20px]">
                Phone Support: **************** / Email Support:
                <a href="mailto:<EMAIL>" class="underline"><EMAIL></a>
              </p>
              <p className="mb-[20px]">
                Vegas Coins is owned and operated by Vegas Coins Inc. located at
                8 The Grn, Ste 18927, Dover, DE, USA 19901
              </p>
            </div>`,
              }}
            />

            {/*  <div className="text-[13px] text-pink-5  leading-1.5 mb-4 max-sm:text-center flex gap-1 flex-col">
              <p className="mb-[20px]">
                Vegas Coins offers social play only and does not involve any
                real money gambling. Vegas Coins is solely for entertainment
                purposes and no real money is required to access and use Vegas
                Coins or to play the games. Users must be 21+ to access our
                platform. © 2024 Vegas Coins Inc. All rights reserved.
              </p>
              <p className="mb-[20px]">
                Phone Support: **************** / Email Support:
                <EMAIL>
              </p>
              <p className="mb-[20px]">
                Vegas Coins is owned and operated by Vegas Coins Inc. located at
                8 The Grn, Ste 18927, Dover, DE, USA 19901
              </p>
            </div> */}
            <div className="flex items-center w-full gap-8 max-sm:justify-center">
              <Image
                src={FooterNumber}
                alt="Number"
                width={175}
                height={48}
                className="hidden"
              />
              <Image src={AgeLogo} alt="Age" width={40} height={40} />
              {/* Trust Pilot link */}
              {/* <Link
                href="https://www.trustpilot.com/review/vegascoins.com "
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image src={TrustPilot} alt="Age" width={100} height={100} />
              </Link> */}
            </div>
          </div>
          <div className="md:col-end-13 md:col-span-4 max-md:order-1">
            <Contact />
          </div>
        </div>
      </footer>
    </div>
  );
}
