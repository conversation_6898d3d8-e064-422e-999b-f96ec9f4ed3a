export const dateOptions = {
	autoHide: true,
	todayBtn: false,
	clearBtn: true,
	clearBtnText: "Clear",
	maxDate: new Date("2030-01-01"),
	minDate: new Date("1950-01-01"),
	theme: {
		background: "bg-gray-500",
		todayBtn: "",
		clearBtn: "bg-gray-8 text-white",
		icons: "text-whiteOpacity-400",
		text: "bg-gray-8 text-whiteOpacity-400",
		disabledText: "text-whiteOpacity-400",
		input: "bg-gray-500 text-whiteOpacity-400 rounded-[0.313rem] h-[2.875rem] border-solid border-transparent focus:border-green-300 focus:bg-gray-500 focus:outline-none",
		inputIcon: "text-whiteOpacity-400",
		selected: "",
	},
	defaultDate: null,
	language: "en",
	disabledDates: [],
	inputNameProp: "date",
	inputIdProp: "date",
	inputPlaceholderProp: "Select Date",
	inputDateFormatProp: {
		day: "numeric",
		month: "long",
		year: "numeric"
	}
}
