"use client";

import React from "react";
import useModalsStore from "@/store/useModalsStore";
import Image from "next/image";
import { PinkButton } from "../Common/Button";
import Signup from "../Auth/Signup";
import LabelOne from "../../../public/assets/img/png/label-1.png";
import LabelTwo from "../../../public/assets/img/png/label-2.png";
import LabelThree from "../../../public/assets/img/png/label-3.png";

const GetStarted = () => {
  const { openModal, closeModal, setIsCloseNeed, clearModals } =
    useModalsStore();

  return (
    <div className="mt-8">
      <h3 className="text-center text-4xl max-md:text-xl font-bold capitalize text-white mb-7">
        Get Started With Vegas Coins Social Casino Today!
      </h3>
      <div className="rounded-lg  bg-custom-gradient p-8">
        <div className=" grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
            <h4 className="font-bold text-xl">Register Account.</h4>
            <Image
              height={1000}
              width={1000}
              className="max-w-[260px]"
              src={LabelOne}
              alt="card-game"
            />
            <p className="font-medium text-sm md:text-lg">
              Create Your Account In Under 10 Seconds.
            </p>
          </div>
          <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
            <h4 className="font-bold text-xl">Verify Details.</h4>
            <Image
              height={1000}
              width={1000}
              className="max-w-[260px]"
              src={LabelTwo}
              alt="card-game"
            />
            <p className="font-medium text-sm md:text-lg">
              Verify Your Personal Details & KYC.
            </p>
          </div>

          <div className="flex justify-start max-md:items-center text-white items-start flex-col gap-4">
            <h4 className="font-bold text-xl">Claim Your Offers!</h4>
            <Image
              height={1000}
              width={1000}
              className="max-w-[260px]"
              src={LabelThree}
              alt="card-game"
            />
            <p className="font-medium text-sm md:text-lg">
              Claim Your Welcome & Daily Bonuses.
            </p>
          </div>
        </div>
        <div className="flex w-full justify-center mt-6">
          <PinkButton
            onClick={() => openModal(<Signup />)}
            className=" max-w-[600px] w-full [&>.thirdSpan]:max-sm:flex [&>.thirdSpan]:max-sm:justify-center [&>.thirdSpan]:max-sm:max-w-full [&>.thirdSpan]:text-sm [&>.secondSpan]:max-sm:max-w-full [&>.firstSpan]:max-sm:max-w-full"
          >
            CREATE ACCOUNT
          </PinkButton>
        </div>
      </div>
    </div>
  );
};

export default GetStarted;
