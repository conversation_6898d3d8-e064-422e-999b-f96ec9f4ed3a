"use client";


import React from "react";
import GameCard from "../GameCard";
import { useRouter } from "next/navigation";

const DemoGame = ({ demoGamesList }) => {
  const router = useRouter();

  const handleDemoGameClick = (gameName) =>{
    router.push(`/demoGame/${gameName}`)
  }

  return (
    <div className="grid mt-8 grid-cols-2 md:grid-cols-4 gap-6 ">
      {demoGamesList?.map((game) => (
        <GameCard key={game?.id} game={game} handleGameClick={()=>handleDemoGameClick(game?.name)} isDemo={true}/>
      ))}
    </div>
  );
};

export default DemoGame;
