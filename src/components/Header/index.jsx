"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import logo from "../../assets/images/logo/logo.svg";
import Mobilelogo from "../../assets/images/logo/Mobilelogo.svg";
import HeaderOptions from "./HeaderOptions";
import MobileMenuDropdown from "./MobileMenuDropdown";
import RedirectLinkToLobby from "./RedirectLinkToLobby";

const Header = () => {
  return (
    <header className="bg-primary-100 h-20 flex items-center justify-between px-4 md:px-8 xl:px-10 2xl:px-28 fixed top-0 w-full gap-4 z-[22]">
      <div className="flex items-center gap-4">
        <MobileMenuDropdown />
        <RedirectLinkToLobby>
          <Image
            src={logo}
            alt="VegasCoins Logo"
            width={235}
            height={40}
            className="h-auto hidden sm:flex max-lg:max-w-36"
          />
          <Image
            src={Mobilelogo}
            alt="VegasCoins Logo"
            width={52}
            height={40}
            className="sm:hidden"
          />
        </RedirectLinkToLobby>
      </div>
      <HeaderOptions />
    </header>
  );
};

export default Header;
