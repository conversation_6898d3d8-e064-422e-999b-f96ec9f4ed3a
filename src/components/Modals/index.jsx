"use client";

import useModalsStore from "@/store/useModalsStore";
import React from "react";
import { Dialog, DialogBackdrop, DialogPanel } from "@headlessui/react";

function Modals() {
  const { components, closeModal,isCloseNeed } = useModalsStore();

const handleCloseModal =() =>{
  if(!isCloseNeed ){
    closeModal()
  }
}
  return components.length > 0
    ? components.map((Component, i) => (
        <Dialog
          key={i}
          open={true}
          onClose={handleCloseModal}
          className="relative z-10"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-backDrop-100 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex justify-center p-4 items-center min-h-dvh" >
              <DialogPanel className='w-full flex items-center justify-center max-w-[991px]' transition>{Component}</DialogPanel>
            </div>
          </div>
        </Dialog>
      ))
    : null;
}

export default Modals;
