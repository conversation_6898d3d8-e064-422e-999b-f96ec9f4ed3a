import dynamic from 'next/dynamic'
import GameSlider from "@/components/GameSlider";
import BannerSection from "@/components/BannerSection";
import Crown from "../../assets/images/casino/crown.svg";
import New from "../../assets/images/casino/new.svg";
import Machine from "../../assets/images/casino/machine.svg";
import Casino from "../../assets/images/casino/casino.svg";
import CategorySlider from "@/components/CategorySlider";
import SearchIcon from "../../../public/assets/img/svg/SearchIcon";
import CategorySelect from "@/components/CategorySelect";
import { subCategories } from "@/config/data";
import PromotionalSection from "@/components/PromotionalSection/PromotionalSection";
import { getBanners, getProviderListing, getSubCategories } from "@/actions";
// import GamesSection from "@/components/GamesSection";
const GamesSection = dynamic(() => import("@/components/GamesSection"), {
  loading: () => <p>Loading...</p>,
});
// import GameCoinSlider from "@/components/GameCoinSlider";
// import SearchGames from "@/components/SearchField";
const SearchGames = dynamic(() => import("@/components/SearchField"), {
  loading: () => <p>Loading...</p>,
});

const LoggedInPage = async () => {
  const gamesData = [
    { icon: Crown, title: "Recommended" },
    { icon: New, title: "New" },
    { icon: Machine, title: "Slot" },
    { icon: Casino, title: "Table Games" },
  ];

  const subCategories = await getSubCategories();

  const bannersData = await getBanners();
  let promotionalBanner = [];
  if (bannersData) {
    if (Array.isArray(bannersData)) {
      promotionalBanner =
        (bannersData || []).filter(
          (banner) => banner.pageName === "promotionPage"
        ) || [];
    } else {
      promotionalBanner = [];
    }
  }

  let lobbySlider = [];
  if (bannersData) {
    if (Array.isArray(bannersData)) {
      lobbySlider =
        (bannersData || [])?.filter(
          (banner) => banner?.pageName == "lobbySlider"
        ) || [];
    } else {
      lobbySlider = [];
    }
  }

  const getAllProviders = await getProviderListing();
  const providerOptions = [
    { value: null, label: "All" },
    ...(getAllProviders && Array.isArray(getAllProviders)
      ? getAllProviders
          .filter((provider) => provider.isActive)
          .map((provider) => ({
            value: provider.masterCasinoProviderId,
            label: provider.name,
          }))
      : []),
  ];
  return (
    <div className="flex flex-col">
      <BannerSection
        promotionalBanner={promotionalBanner}
        lobbySlider={lobbySlider}
      />
      <CategorySlider
        subCategories={subCategories}
        className="mt-5 sm:mt-6  mb-7"
      />
      <div className="w">
        <form className="w-full mx-auto">
          {/* <label for="default-search" className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label> */}
          <div className="relative">
            <SearchGames />
            <CategorySelect
              providers={providerOptions}
              className="absolute z-[21] right-2 max-md:right-2 top-1/2 -translate-y-1/2 "
            />
          </div>
        </form>
      </div>
      {/* <GameCoinSlider className='mt-5'/> */}
      <div className="py-4">
        <GamesSection subCategories={subCategories} />
      </div>
    </div>
  );
};

export default LoggedInPage;
