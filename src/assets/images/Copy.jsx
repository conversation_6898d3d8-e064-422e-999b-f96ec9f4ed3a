import React from 'react';
import { toast } from 'react-toastify';

const Copy = ({ userId, className }) => {
    const handleCopy = () => {
        if (userId) {
            navigator.clipboard.writeText(userId)
                .then(() => {
                    toast.success('User ID copied to clipboard!');
                })
                .catch(err => {
                    toast.error('Failed to copy: ', err);
                });
        }
    };

    return (
        <div
            onClick={handleCopy}
            className={`flex items-center justify-center cursor-pointer transition-colors duration-200 ${className}`}
            style={{ width: '24px', height: '24px' }} 
        >
            <svg
                width="18"
                height="19"
                viewBox="0 0 18 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="transition-colors duration-200" 
            >
                <path
                    d="M15.1875 2.75H6.1875C6.03832 2.75 5.89524 2.80926 5.78975 2.91475C5.68426 3.02024 5.625 3.16332 5.625 3.3125V6.125H2.8125C2.66332 6.125 2.52024 6.18426 2.41475 6.28975C2.30926 6.39524 2.25 6.53832 2.25 6.6875V15.6875C2.25 15.8367 2.30926 15.9798 2.41475 16.0852C2.52024 16.1907 2.66332 16.25 2.8125 16.25H11.8125C11.9617 16.25 12.1048 16.1907 12.2102 16.0852C12.3157 15.9798 12.375 15.8367 12.375 15.6875V12.875H15.1875C15.3367 12.875 15.4798 12.8157 15.5852 12.7102C15.6907 12.6048 15.75 12.4617 15.75 12.3125V3.3125C15.75 3.16332 15.6907 3.02024 15.5852 2.91475C15.4798 2.80926 15.3367 2.75 15.1875 2.75ZM14.625 11.75H12.375V6.6875C12.375 6.53832 12.3157 6.39524 12.2102 6.28975C12.1048 6.18426 11.9617 6.125 11.8125 6.125H6.75V3.875H14.625V11.75Z"
                    fill="currentColor" 
                />
            </svg>
        </div>
    );
};

export default Copy;
