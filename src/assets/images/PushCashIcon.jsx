import React from "react";

const PushCashIcon = () => {
  return (
    <svg
      width="110"
      height="50"
      viewBox="0 0 847 374"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M827.782 264.004C824.262 260.856 823.868 259.675 823.868 254.581H823.846C823.996 242.762 824.204 231.318 824.403 220.334L824.403 220.32L824.404 220.308C824.726 202.509 825.027 185.917 825.027 170.887C825.027 116.774 803.447 88.1326 763.83 88.1326C731.669 88.1326 711.292 105.777 701.475 134.025V0L614.786 10.1885V10.9756L634.004 29.0131C637.13 32.1615 637.524 33.7357 637.524 38.8299V254.559C637.524 259.675 637.152 260.834 634.004 263.982L614.786 282.02V282.807H724.607V282.02L705.389 263.982C701.869 260.856 701.475 259.675 701.475 254.056V138.332C708.537 128.668 717.567 120.688 730.904 120.688C754.823 120.688 760.704 144.213 760.704 179.916C760.704 191.992 760.218 217.039 759.87 235.001C759.675 245.04 759.523 252.866 759.523 254.975C759.523 259.304 759.13 260.856 755.61 264.004L737.179 282.042V282.829H847V282.042L827.782 264.004ZM22.7601 120.644C22.7601 115.55 22.3666 114.369 19.2401 110.827L0 91.2154V90.4283H86.6896V132.735C100.026 101.754 121.212 88.0233 147.493 88.0233C191.023 88.0233 221.632 124.885 221.632 184.114C221.632 247.257 185.951 288.448 138.485 288.448C110.237 288.448 93.7734 277.079 86.7115 258.626V343.391C86.7115 348.486 87.105 349.666 92.5928 353.208L121.606 373.213V374.001H0V373.213L19.2182 355.176C22.3666 352.028 22.7383 350.869 22.7383 345.753V120.644H22.7601ZM86.6896 137.851V255.915C92.1774 261.796 101.601 265.731 112.183 265.731C132.188 265.731 153.767 244.546 153.767 187.285C153.767 139.425 138.856 119.42 116.118 119.42C103.962 119.42 93.7516 127.269 86.6896 137.851ZM309.896 288.404C271.066 288.404 245.77 260.156 245.77 206.809C245.77 195.461 246.338 138.113 246.557 120.25C246.557 115.156 246.163 113.975 243.037 110.827L223.819 91.2153V90.4282H310.508C310.508 90.4282 310.115 173.991 310.115 197.779C310.115 232.695 313.875 256.221 337.182 256.221C360.489 256.221 384.626 226.792 384.626 226.792C384.491 227.197 384.334 227.684 384.153 228.245L384.151 228.251C380.193 240.548 364.795 288.382 309.918 288.382L309.896 288.404ZM424.79 90.4501H329.617V91.434L424.79 186.607V90.4501ZM601.361 168.372L538.897 105.908V105.886C527.418 94.4075 498.405 101.36 497.268 120.928C496.219 139.009 510.539 148.673 532.622 157.987L558.421 169.291C582.733 179.895 601.951 193.647 601.951 223.425C601.951 270.06 559.12 288.535 514.65 288.535C502.45 288.535 491.934 286.649 482.842 285.018C477.025 283.975 471.792 283.036 467.074 282.763C464.888 282.636 462.547 282.673 459.87 282.715C458.397 282.738 456.822 282.763 455.115 282.763H424.812V186.607C424.812 186.607 482.182 243.999 501.313 263.13C506.801 268.617 517.536 276.029 527.265 276.029C542.417 276.029 552.037 264.441 552.037 251.105C552.037 235.036 542.89 231.216 525.914 224.126C521.893 222.446 517.433 220.584 512.551 218.331L485.462 205.781C463.882 195.571 447.419 177.927 447.419 150.072C447.419 112.817 478.443 88.1107 528.249 88.1107C533.372 88.1107 538.807 88.6455 544.574 89.2128C549.366 89.6843 554.386 90.1782 559.645 90.4064C562.291 90.5157 602.367 90.2752 602.367 90.2752V168.372H601.361Z"
        fill="#1A00FD"
      />
    </svg>
  );
};

export default PushCashIcon;
