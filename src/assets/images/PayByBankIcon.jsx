import React from "react";

const PayByBank = () => {
  return (
    <svg width="110" height="60" viewBox="0 0 110 60" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M100.788 38.874C100.417 38.874 99.9786 38.8234 99.4893 38.7222V41.6409C99.4893 42.0964 99.3375 42.2482 98.8988 42.2482H97.6166C97.1611 42.2482 97.0092 42.0964 97.0092 41.6409V31.2314C97.0092 30.6915 97.1442 30.489 97.6672 30.2697C98.5614 29.9154 99.6749 29.7636 101.008 29.7636C103.437 29.7636 104.601 30.9952 104.601 33.1884V35.2804C104.601 37.6087 103.319 38.874 100.788 38.874ZM102.087 33.2053C102.087 32.3955 101.632 31.9906 100.704 31.9906C100.333 31.9906 99.9448 32.0243 99.5737 32.1087C99.523 32.1255 99.4893 32.1593 99.4893 32.2268V36.5289C99.8942 36.5964 100.282 36.6301 100.586 36.6301C101.598 36.6301 102.087 36.1915 102.087 35.2804V33.2053ZM91.8129 38.874C91.4418 38.874 91.0031 38.8234 90.5138 38.7222V41.6409C90.5138 42.0964 90.362 42.2482 89.9234 42.2482H88.6411C88.1856 42.2482 88.0338 42.0964 88.0338 41.6409V31.2314C88.0338 30.6915 88.1688 30.489 88.6918 30.2697C89.5859 29.9154 90.6994 29.7636 92.0322 29.7636C94.4617 29.7636 95.6258 30.9952 95.6258 33.1884V35.2804C95.6089 37.6087 94.3267 38.874 91.8129 38.874ZM93.112 33.2053C93.112 32.3955 92.6565 31.9906 91.7286 31.9906C91.3574 31.9906 90.9694 32.0243 90.5982 32.1087C90.5476 32.1255 90.5138 32.1593 90.5138 32.2268V36.5289C90.9187 36.5964 91.3068 36.6301 91.6105 36.6301C92.6227 36.6301 93.112 36.1915 93.112 35.2804V33.2053ZM83.3605 26.6255C81.6396 26.6255 80.4755 26.1194 79.8344 25.0903C79.5476 24.6853 79.6319 24.3479 80.0875 24.1117L81.0322 23.6393C81.4878 23.3863 81.7071 23.4538 81.9939 23.8587C82.2976 24.2804 82.7362 24.4829 83.3267 24.4829C84.204 24.4829 84.6427 24.0442 84.6427 23.1501V22.4752C84.2546 22.5596 83.7316 22.5933 83.1074 22.5933C80.7286 22.5933 79.5476 21.4461 79.5476 19.1685V14.8495C79.5476 14.3939 79.6994 14.2421 80.1549 14.2421H81.4203C81.8758 14.2421 82.0276 14.3939 82.0276 14.8495V19.0841C82.0276 19.9108 82.4832 20.3326 83.428 20.3326C83.7991 20.3326 84.204 20.2988 84.6258 20.2145V14.8495C84.6258 14.3939 84.7776 14.2421 85.2332 14.2421H86.5154C86.954 14.2421 87.1059 14.3939 87.1059 14.8495V22.9645C87.1059 25.2421 85.9249 26.6255 83.3605 26.6255ZM82.8375 29.7636C85.3007 29.7636 86.5154 30.9277 86.5154 33.2559V37.4568C86.5154 38.0304 86.3804 38.216 85.908 38.4185C85.25 38.7222 84.204 38.8571 82.7868 38.8571C81.3528 38.8571 80.3743 38.6378 79.8344 38.1992C79.2945 37.7605 79.0077 37.0688 79.0077 36.124C79.0077 34.0826 80.0875 33.4077 82.8543 33.4077C83.2761 33.4077 83.681 33.4246 84.0184 33.4415V33.0703C84.0184 32.2605 83.5967 31.8387 82.7531 31.8387C82.1457 31.8387 81.6902 32.0412 81.3865 32.4461C81.0997 32.8341 81.0154 32.8847 80.543 32.716L79.6319 32.3617C79.227 32.1761 79.1089 31.9062 79.2945 31.4844C79.9187 30.3541 81.0829 29.7636 82.8375 29.7636ZM83.3773 35.3479C83.0399 35.3311 82.8037 35.3311 82.635 35.3311C81.7577 35.3311 81.5046 35.6179 81.5046 36.124C81.5046 36.647 81.7577 36.9338 82.6181 36.9338C83.1918 36.9338 83.6473 36.9169 83.9172 36.8663C84.0016 36.8495 84.0353 36.7988 84.0353 36.7482V35.3648C83.9172 35.3648 83.7148 35.3648 83.3773 35.3479ZM74.1994 23.0657C72.9172 23.0657 71.8712 22.8801 71.0783 22.5258C70.5552 22.2559 70.4709 22.1209 70.4709 21.5979V11.1884C70.4709 10.7329 70.6227 10.5811 71.0783 10.5811H72.3605C72.7991 10.5811 72.951 10.7329 72.951 11.1884V14.1409C73.4571 14.0228 73.997 13.9722 74.5031 13.9722C76.8819 13.9722 78.0629 15.2038 78.0629 17.397V19.4722C78.0798 21.851 76.7976 23.0657 74.1994 23.0657ZM75.566 17.4139C75.566 16.6041 75.1105 16.1992 74.1826 16.1992C73.7776 16.1992 73.3727 16.2666 72.9678 16.3847V20.6194C72.9678 20.67 72.9847 20.6869 73.0184 20.7038C73.3052 20.7881 73.6427 20.8387 74.0813 20.8387C75.0936 20.8387 75.5829 20.3832 75.5829 19.4722V17.4139H75.566ZM72.3773 30.5228C72.6641 30.0841 72.8329 30.0335 73.5414 30.0335H74.6381C75.2454 30.0335 75.3973 30.2866 75.043 30.759L72.8329 33.7789C73.1703 34.0151 73.474 34.4031 73.7608 34.9768L75.1948 37.8955C75.4816 38.4185 75.3804 38.6378 74.7899 38.6378H73.6595C72.951 38.6378 72.7822 38.5534 72.5629 38.0811L71.6856 36.0396C71.5 35.601 71.1457 35.3817 70.6227 35.3817H70.0491V38.0136C70.0491 38.486 69.8973 38.6378 69.4249 38.6378H68.1933C67.7209 38.6378 67.5691 38.486 67.5691 38.0136V27.0304C67.5691 26.558 67.7209 26.4062 68.1933 26.4062H69.4249C69.8973 26.4062 70.0491 26.558 70.0491 27.0304V33.1715H70.4034H70.5046L72.3773 30.5228ZM65.3927 38.6209H64.1105C63.6549 38.6209 63.5031 38.4691 63.5031 38.0136V33.2896C63.5031 32.463 63.0476 32.0412 62.1028 32.0412C61.681 32.0412 61.293 32.0918 60.9556 32.193C60.9218 32.193 60.9049 32.2099 60.9049 32.2605V38.0136C60.9049 38.4691 60.7531 38.6209 60.2976 38.6209H59.0322C58.5767 38.6209 58.4249 38.4691 58.4249 38.0136V31.3326C58.4249 30.7927 58.5599 30.5903 59.0829 30.3709C60.0445 29.966 61.158 29.7804 62.4402 29.7804C64.8191 29.7804 66 30.9277 66 33.2053V38.0136C65.9832 38.4691 65.8313 38.6209 65.3927 38.6209ZM61.8497 26.6255C60.1289 26.6255 58.9648 26.1194 58.3237 25.0903C58.0368 24.6853 58.1212 24.3479 58.5767 24.1117L59.5215 23.6393C59.977 23.3863 60.1964 23.4538 60.4832 23.8587C60.7868 24.2804 61.2255 24.4829 61.816 24.4829C62.6933 24.4829 63.1319 24.0442 63.1319 23.1501V22.4752C62.7439 22.5596 62.2209 22.5933 61.5967 22.5933C59.2178 22.5933 58.0368 21.4461 58.0368 19.1685V14.8495C58.0368 14.3939 58.1887 14.2421 58.6442 14.2421H59.9095C60.3651 14.2421 60.5169 14.3939 60.5169 14.8495V19.0841C60.5169 19.9108 60.9724 20.3326 61.9172 20.3326C62.2884 20.3326 62.6933 20.2988 63.1151 20.2145V14.8495C63.1151 14.3939 63.2669 14.2421 63.7224 14.2421H65.0046C65.4433 14.2421 65.5951 14.3939 65.5951 14.8495V22.9645C65.612 25.2421 64.431 26.6255 61.8497 26.6255ZM53.178 38.874C51.7439 38.874 50.7654 38.6547 50.2255 38.216C49.6856 37.7774 49.3988 37.0857 49.3988 36.1409C49.3988 34.0995 50.4786 33.4246 53.2454 33.4246C53.6672 33.4246 54.0721 33.4415 54.4095 33.4584V33.0872C54.4095 32.2774 53.9878 31.8556 53.1442 31.8556C52.5368 31.8556 52.0813 32.058 51.7776 32.463C51.4908 32.851 51.4065 32.9016 50.9341 32.7329L50.023 32.3449C49.6181 32.1593 49.5 31.8893 49.6856 31.4676C50.293 30.3203 51.474 29.7467 53.2117 29.7467C55.6749 29.7467 56.8896 30.9108 56.8896 33.239V37.44C56.8896 38.0136 56.7546 38.1992 56.2822 38.4016C55.6243 38.7222 54.5951 38.874 53.178 38.874ZM54.4095 35.3648C54.3083 35.3648 54.089 35.3648 53.7684 35.3479C53.431 35.3311 53.1948 35.3311 53.0261 35.3311C52.1488 35.3311 51.8957 35.6179 51.8957 36.124C51.8957 36.647 52.1488 36.9338 52.9924 36.9338C53.566 36.9338 54.0215 36.9169 54.2914 36.8663C54.3758 36.8495 54.4095 36.7988 54.4095 36.7482V35.3648ZM52.7393 23.0657C51.3053 23.0657 50.3267 22.8464 49.7868 22.4077C49.247 21.9691 48.9602 21.2774 48.9602 20.3326C48.9602 18.2912 50.0399 17.6163 52.8068 17.6163C53.2286 17.6163 53.6335 17.6332 53.9709 17.6501V17.2789C53.9709 16.4691 53.5491 16.0473 52.7056 16.0473C52.0982 16.0473 51.6427 16.2498 51.339 16.6547C51.0522 17.0427 50.9678 17.0933 50.4954 16.9246L49.5844 16.5366C49.1795 16.351 49.0614 16.0811 49.247 15.6593C49.8543 14.512 51.0353 13.9384 52.773 13.9384C55.2362 13.9384 56.451 15.1025 56.451 17.4307V21.6317C56.451 22.2053 56.316 22.3909 55.8436 22.5933C55.1856 22.9139 54.1565 23.0657 52.7393 23.0657ZM53.9709 19.5565C53.8697 19.5565 53.6503 19.5565 53.3298 19.5396C52.9924 19.5228 52.7562 19.5228 52.5875 19.5228C51.7102 19.5228 51.4571 19.8096 51.4571 20.3157C51.4571 20.8387 51.7102 21.1255 52.5537 21.1255C53.1273 21.1255 53.5829 21.1087 53.8528 21.058C53.9372 21.0412 53.9709 20.9906 53.9709 20.9399V19.5565ZM44.4724 38.6715H39.7485C39.2424 38.6715 39.0737 38.5028 39.0737 37.9967V27.5534C39.0737 27.0304 39.2424 26.8786 39.7485 26.8786H44.4049C46.6151 26.8786 47.931 28.0258 47.931 30.0166C47.931 31.1301 47.543 31.94 46.7838 32.4798V32.5473C47.8467 33.0028 48.3697 33.9645 48.3697 35.3985C48.3697 36.4277 47.9985 37.2206 47.2899 37.8111C46.5645 38.3847 45.6197 38.6715 44.4724 38.6715ZM43.9832 29.2574H41.7562V31.5182H43.9832C44.793 31.5182 45.1979 31.1301 45.1979 30.3878C45.1979 29.6455 44.793 29.2574 43.9832 29.2574ZM44.3037 33.8633H41.773V36.2758H44.3037C45.1304 36.2758 45.5522 35.8709 45.5522 35.078C45.5522 34.2682 45.1304 33.8633 44.3037 33.8633ZM44.1856 18.9998H41.773V22.1378C41.773 22.6439 41.6043 22.8126 41.0982 22.8126H39.7654C39.2592 22.8126 39.0905 22.6439 39.0905 22.1378V11.7452C39.0905 11.239 39.2592 11.0703 39.7654 11.0703H44.1856C46.6826 11.0703 48.2178 12.5719 48.2178 15.035C48.2347 17.5151 46.6994 18.9998 44.1856 18.9998ZM43.9157 13.5166H41.7899V16.5197H43.9157C44.8942 16.5197 45.451 15.9798 45.451 15.035C45.451 14.1071 44.8942 13.5166 43.9157 13.5166ZM32.5276 38.6884H25.0706H21.4095H14.273C13.3283 38.6884 12.5522 37.9123 12.5522 36.9676C12.5522 36.8832 12.5522 36.7988 12.5691 36.7314C12.5691 36.4108 12.6534 36.0734 12.839 35.7866L20.6503 23.7237H15.7577H12.5522H7.15341C6.96783 23.7237 6.81599 23.8755 6.81599 24.0611V42.94C6.81599 43.1255 6.96783 43.2774 7.15341 43.2774H16.8037C16.9893 43.2774 17.1411 43.1255 17.1411 42.94V42.5857V41.6746C17.1411 41.2866 17.4448 40.9829 17.8329 40.9829H19.908C20.296 40.9829 20.5997 41.2866 20.5997 41.6746V42.6025V43.7498V46.0442C20.5997 47.0565 19.773 47.8832 18.7608 47.8832H5.21322C4.20095 47.8832 3.37427 47.0565 3.37427 46.0442V22.1209C3.37427 21.1087 4.20095 20.282 5.21322 20.282H12.5522H15.7577H23.6872C24.6319 20.282 25.408 21.0581 25.408 22.0028C25.408 22.0872 25.408 22.1715 25.3911 22.239C25.3911 22.5596 25.3068 22.897 25.1212 23.1838L17.3099 35.2298H21.4095H25.0706H30.5875C30.773 35.2298 30.9249 35.078 30.9249 34.8924V14.8663C30.9249 14.6807 30.773 14.5289 30.5875 14.5289H20.9372C20.7516 14.5289 20.5997 14.6807 20.5997 14.8663V15.2038V16.351V17.2789C20.5997 17.6669 20.296 17.9706 19.908 17.9706H17.8497C17.4617 17.9706 17.158 17.6669 17.158 17.2789V16.351V15.2038V12.9093C17.158 11.897 17.9847 11.0703 18.997 11.0703H32.5445C33.5568 11.0703 34.3835 11.897 34.3835 12.9093V36.8495C34.3666 37.8617 33.5399 38.6884 32.5276 38.6884ZM11.9786 46.5166C12.4847 46.5166 12.8896 46.1117 12.8896 45.6056C12.8896 45.0995 12.4847 44.6946 11.9786 44.6946C11.4724 44.6946 11.0675 45.0995 11.0675 45.6056C11.0506 46.0949 11.4724 46.5166 11.9786 46.5166Z" fill="#262626"/>
    </svg>
    
  );
};

export default PayByBank;
