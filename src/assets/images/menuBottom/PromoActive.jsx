import React from 'react'

const PromoActiveIcon = () => {
    return (
        <svg width="35" height="35" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.5 7.25H17.2113C17.2478 7.21906 17.2853 7.18906 17.3209 7.15625C17.6057 6.90329 17.8351 6.5943 17.995 6.24861C18.1549 5.90292 18.2417 5.52796 18.25 5.14718C18.2623 4.73063 18.1894 4.31595 18.0356 3.92861C17.8819 3.54127 17.6506 3.18944 17.356 2.89471C17.0613 2.59999 16.7096 2.3686 16.3223 2.21474C15.935 2.06088 15.5203 1.98781 15.1038 2C14.7228 2.0082 14.3477 2.09495 14.0018 2.25482C13.656 2.41468 13.3468 2.64422 13.0938 2.92906C12.7436 3.3349 12.4589 3.79291 12.25 4.28656C12.0411 3.79291 11.7564 3.3349 11.4062 2.92906C11.1532 2.64422 10.844 2.41468 10.4982 2.25482C10.1523 2.09495 9.77718 2.0082 9.39625 2C8.97969 1.98781 8.56503 2.06088 8.17774 2.21474C7.79044 2.3686 7.43868 2.59999 7.14405 2.89471C6.84941 3.18944 6.61812 3.54127 6.46438 3.92861C6.31064 4.31595 6.23768 4.73063 6.25 5.14718C6.25833 5.52796 6.34514 5.90292 6.505 6.24861C6.66486 6.5943 6.89434 6.90329 7.17906 7.15625C7.21469 7.18718 7.25219 7.21718 7.28875 7.25H4C3.60218 7.25 3.22064 7.40803 2.93934 7.68934C2.65804 7.97064 2.5 8.35217 2.5 8.75V11.75C2.5 12.1478 2.65804 12.5294 2.93934 12.8107C3.22064 13.092 3.60218 13.25 4 13.25V19.25C4 19.6478 4.15804 20.0294 4.43934 20.3107C4.72064 20.592 5.10218 20.75 5.5 20.75H11.125C11.2245 20.75 11.3198 20.7105 11.3902 20.6402C11.4605 20.5698 11.5 20.4745 11.5 20.375V11.75H4V8.75H11.5V11.75H13V8.75H20.5V11.75H13V20.375C13 20.4745 13.0395 20.5698 13.1098 20.6402C13.1802 20.7105 13.2755 20.75 13.375 20.75H19C19.3978 20.75 19.7794 20.592 20.0607 20.3107C20.342 20.0294 20.5 19.6478 20.5 19.25V13.25C20.8978 13.25 21.2794 13.092 21.5607 12.8107C21.842 12.5294 22 12.1478 22 11.75V8.75C22 8.35217 21.842 7.97064 21.5607 7.68934C21.2794 7.40803 20.8978 7.25 20.5 7.25ZM8.17281 6.03125C8.04168 5.9125 7.93651 5.76791 7.86391 5.60658C7.79131 5.44525 7.75285 5.27065 7.75094 5.09375C7.7462 4.88647 7.78287 4.68033 7.85881 4.4874C7.93476 4.29448 8.04844 4.11865 8.1932 3.97022C8.33796 3.82179 8.51089 3.70375 8.70186 3.62301C8.89282 3.54226 9.09798 3.50044 9.30531 3.5H9.35125C9.52815 3.5019 9.70275 3.54037 9.86409 3.61297C10.0254 3.68556 10.17 3.79073 10.2888 3.92187C11.0753 4.81062 11.3528 6.28437 11.4503 7.19562C10.5353 7.09906 9.0625 6.82156 8.17281 6.03125ZM16.3291 6.03125C15.4394 6.81875 13.9628 7.09625 13.0478 7.19375C13.1594 6.20843 13.4688 4.76562 14.2188 3.92281C14.3375 3.79167 14.4821 3.6865 14.6434 3.6139C14.8047 3.5413 14.9793 3.50284 15.1562 3.50093H15.2022C15.4095 3.50223 15.6145 3.5449 15.8052 3.62644C15.9958 3.70798 16.1683 3.82675 16.3124 3.97579C16.4566 4.12483 16.5695 4.30115 16.6447 4.49441C16.7198 4.68766 16.7556 4.89397 16.75 5.10125C16.7469 5.27695 16.7078 5.45016 16.6351 5.61013C16.5624 5.7701 16.4576 5.91344 16.3272 6.03125H16.3291Z" fill="#33CC33" />
        </svg>
    )
}

export default PromoActiveIcon