import React from "react";

const AeropayIcon = () => {
  return (
    <svg
      width="35"
      height="35"
      viewBox="0 0 585 104"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M206.195 82.4049V79.4698L208.118 77.1217L208.413 76.0904C207.671 76.971 206.785 77.7087 205.899 78.4385C201.462 81.6671 195.851 83.2854 189.052 83.2854C185.061 83.2854 181.222 82.5476 177.527 80.9373C173.832 79.319 170.584 77.2645 167.775 74.4722C164.965 71.6799 162.747 68.4513 161.27 64.7784C159.794 61.1056 159.051 57.29 159.051 53.3236C159.051 49.3573 159.794 45.3909 161.27 41.7181C162.747 38.0452 165.109 34.6659 167.918 31.8736C170.576 29.0813 173.832 26.7332 177.527 25.2657C181.222 23.7981 185.061 22.9176 188.908 23.0604C195.851 23.0604 201.622 24.8214 206.051 28.2008C206.793 28.7878 207.679 29.5255 208.27 30.1125L207.974 29.232L206.051 26.8839V23.5046H222.013V82.2621H206.203V82.4049H206.195ZM190.376 37.4582C188.309 37.4582 186.242 37.7517 184.319 38.6323C182.396 39.37 180.624 40.6868 179.291 42.1544C176.338 45.2402 174.854 49.2065 174.854 53.3157C174.854 57.4248 176.482 61.5419 179.291 64.477C180.767 65.9445 182.396 67.1186 184.319 67.9991C186.242 68.7368 188.309 69.1731 190.376 69.1731C192.443 69.1731 194.662 68.8796 196.586 67.9991C198.509 67.2613 200.424 66.0873 201.909 64.477C203.385 63.0094 204.566 61.2484 205.309 59.3366C206.051 57.4248 206.49 55.3702 206.49 53.3157C206.49 51.2611 206.043 49.2065 205.309 47.2948C204.566 45.383 203.385 43.6219 201.765 42.1544C200.289 40.6868 198.517 39.5128 196.594 38.6323C194.527 37.8945 192.459 37.4582 190.384 37.4582H190.376Z"
        fill="#01181B"
      />
      <path
        d="M286.881 69.76C280.672 79.0175 271.367 83.7137 258.805 83.7137C249.491 83.7137 241.957 80.7786 236.195 75.0432C230.576 69.0223 227.623 61.2324 227.623 53.1569C227.623 45.0814 230.728 37.1487 236.346 31.2706C242.26 25.3925 249.651 22.4574 258.813 22.4574C267.975 22.4574 275.213 25.2497 280.688 30.8343C286.155 36.4189 288.813 43.6139 288.813 52.2843V54.6324C288.813 55.5129 288.661 56.2507 288.661 56.8377V57.8689H243.441C244.184 61.3911 246.099 64.6276 248.908 66.8329C251.861 69.0382 255.413 70.2122 259.108 70.0615C262.213 70.2122 265.165 69.4745 267.975 68.1497C270.784 66.825 273.003 64.7704 274.774 62.2716L286.889 69.76H286.881ZM258.805 35.5305C251.119 35.5305 246.243 39.0526 244.024 46.2555H272.691C271.949 43.0269 270.177 40.2346 267.663 38.3228C265.149 36.411 262.045 35.3877 258.797 35.5305H258.805Z"
        fill="#01181B"
      />
      <path
        d="M310.681 82.4207H294.575V23.6632H310.537V29.9776L309.651 32.7699C309.651 32.7699 310.537 31.4452 310.689 31.4452C312.612 28.6529 315.126 26.4476 318.079 24.98C321.032 23.5125 324.432 22.6319 327.688 22.7747V38.7829C321.926 38.7829 317.784 40.2505 314.831 43.1935C312.021 46.1286 310.545 50.2457 310.545 55.5288V82.4127H310.696L310.681 82.4207Z"
        fill="#01181B"
      />
      <path
        d="M360.034 22.4733C364.168 22.3226 368.462 23.0603 372.301 24.6786C376.14 26.1461 379.691 28.4942 382.796 31.4372C385.749 34.2295 387.968 37.7516 389.596 41.4245C391.224 45.0973 391.966 49.2065 391.966 53.1728C391.966 57.1391 391.224 61.2483 389.596 64.9211C387.976 68.594 385.757 72.1161 382.796 74.9084C376.587 80.6358 368.462 83.7216 360.186 83.7216C351.91 83.7216 343.634 80.6358 337.425 74.9084C334.615 72.1161 332.253 68.594 330.776 64.9211C329.148 61.2483 328.414 57.1391 328.414 53.1728C328.414 49.2065 329.156 45.0973 330.776 41.4245C332.405 37.7516 334.615 34.2295 337.576 31.4372C340.529 28.5021 344.081 26.1461 347.919 24.6786C351.758 23.0603 355.9 22.3305 360.034 22.4733ZM371.415 41.5752C368.462 38.6401 364.32 36.8711 360.034 36.8711C355.749 36.8711 351.758 38.4894 348.654 41.5752C347.177 43.0428 345.996 44.9545 345.254 46.8663C344.512 48.9209 344.073 50.9754 344.216 53.1807C344.065 55.386 344.512 57.4406 345.254 59.4952C345.996 61.5497 347.177 63.3108 348.654 64.9291C350.13 66.3966 351.758 67.5706 353.682 68.4512C355.605 69.1889 357.672 69.6252 359.739 69.6252C361.958 69.6252 364.025 69.3317 365.948 68.4512C367.872 67.5706 369.787 66.3966 371.272 64.9291C374.224 61.8432 376.004 57.5834 376.004 53.3235C376.004 49.0637 374.376 44.8038 371.423 41.5752H371.415Z"
        fill="#01181B"
      />
      <path
        d="M428.91 83.2774C425.071 83.4282 421.224 82.6904 417.529 81.2229C413.985 79.7553 410.729 77.55 407.928 74.9084L406.89 73.4409L407.928 76.2332V102.237H398.023V24.3772L408.223 23.3459V30.9851L407.337 33.4839L408.223 32.1592C413.395 26.4318 420.338 23.4887 429.061 23.4887C432.9 23.4887 436.89 24.2265 440.442 25.694C443.985 27.1616 447.242 29.3669 450.051 32.1592C455.661 37.7438 458.766 45.383 458.766 53.3157C458.766 61.2483 455.661 68.8875 450.051 74.4721C447.385 77.2645 444.137 79.4697 440.442 80.9373C436.747 82.6983 432.908 83.4361 428.917 83.2854L428.91 83.2774ZM428.463 33.0397C422.996 33.0397 417.672 35.245 413.834 39.0606C409.995 42.8762 407.776 48.1673 407.776 53.6012C407.776 59.0351 409.995 64.3262 413.834 68.1419C417.672 71.9575 422.996 74.1628 428.463 74.1628C431.128 74.1628 433.786 73.7185 436.292 72.6952C438.806 71.664 441.025 70.1964 442.948 68.1419C446.643 64.1755 448.71 59.0351 448.71 53.6012C448.71 48.1673 446.643 43.027 442.948 39.0606C441.025 37.1488 438.806 35.5385 436.292 34.5072C433.778 33.476 431.12 32.889 428.463 33.0397Z"
        fill="#01181B"
      />
      <path
        d="M514.624 82.4127V74.6307C509.301 80.5089 502.358 83.3012 493.635 83.3012C489.796 83.3012 485.805 82.5634 482.254 81.0959C478.71 79.6283 475.454 77.423 472.645 74.6307C467.034 69.0461 463.93 61.4069 463.93 53.4743C463.93 45.5416 467.034 37.9024 472.645 32.3178C475.31 29.5255 478.559 27.3202 482.254 25.8526C485.949 24.3851 489.788 23.6473 493.635 23.6473C497.473 23.4966 501.464 24.2343 505.015 25.7019C508.559 27.1694 511.959 29.3747 514.624 32.167V24.5279H524.672V82.5476L514.624 82.3968V82.4127ZM494.082 33.0555C491.424 33.0555 488.758 33.4997 486.252 34.523C483.746 35.5464 481.52 37.0218 479.604 39.0764C475.909 43.0428 473.842 48.1831 473.842 53.617C473.842 59.0509 475.909 64.1913 479.604 68.1577C481.52 70.0694 483.746 71.6798 486.252 72.711C488.766 73.7423 491.424 74.1786 494.082 74.1786C499.548 74.1786 504.872 71.9733 508.711 68.1577C512.549 64.342 514.768 59.0509 514.768 53.617C514.768 48.1831 512.549 42.892 508.711 39.0764C504.872 35.2608 499.692 33.0555 494.082 33.0555Z"
        fill="#01181B"
      />
      <path
        d="M584.529 24.3931L563.835 80.8024C561.025 88.4416 557.626 93.8755 553.635 97.2548C549.493 100.634 544.178 102.252 537.673 102.252H535.016V93.1457H537.673C544.768 93.1457 550.092 89.1794 553.483 81.3974L530.131 24.401H541.36L558.503 68.3243L574.018 24.401H584.513L584.529 24.3931Z"
        fill="#01181B"
      />
      <path
        d="M32.5381 62.2795C36.6802 60.0742 106.433 21.0057 108.205 20.1173C108.796 19.673 109.538 19.3795 110.272 19.086C111.014 18.7925 111.749 18.9353 112.339 19.2367C112.786 19.681 113.081 20.4108 112.786 21.1485C112.491 21.7355 112.044 22.3226 111.453 22.6161C110.567 23.2031 37.4144 68.594 34.1582 70.6485C32.0912 71.9733 30.1678 73.4408 30.0241 76.0824C29.8725 77.4072 30.4711 78.5812 31.3569 79.4617C32.2428 80.1995 33.5756 80.6358 34.7568 80.6358C35.938 80.6358 37.2708 80.4851 38.452 80.4851C42.1471 80.4851 45.8423 81.8098 48.7952 84.1579C52.1951 87.093 55.148 90.623 57.3667 94.4387C58.6995 96.644 61.0619 100.753 64.1664 100.753H64.3181L65.3556 100.904C68.0132 101.055 70.2319 98.6985 71.86 96.4932C78.8034 87.5293 137.918 9.09083 140.432 5.12449C141.318 3.79973 141.174 1.15814 138.66 0.864632C136.888 0.571122 25.6027 33.3252 20.7264 34.8007L14.9642 36.7125C14.5172 36.8632 13.9267 37.006 13.4877 37.2995L12.0112 37.7437C10.9737 38.0372 9.94418 38.3307 9.0583 38.775C6.83961 39.5127 4.62092 40.3932 2.55387 41.2738C1.96328 41.5673 1.3727 42.0115 1.0774 42.4478C0.630473 43.0348 0.486816 43.7726 0.486816 44.5024C0.486816 45.9699 1.3727 47.2947 2.84916 47.8817C4.91622 48.7622 7.13491 49.6428 9.3536 50.2298C15.4111 52.4351 20.7344 56.1079 24.5732 61.0976C25.3154 62.1288 25.906 63.0093 27.3825 63.1521C28.42 63.3028 29.4495 63.1521 30.487 62.8586L32.5541 62.2716L32.5381 62.2795Z"
        fill="#0FDDF0"
      />
    </svg>
  );
};

export default AeropayIcon;
