import {
  getCookie,
  deleteCookie,
} from "./cookiesHelper";

async function apiCall(
  method,
  endpoint,
  { params = {}, data = {}, headers = {} } = {}
) {
  const url = new URL(`${process.env.NEXT_PUBLIC_API_URL}${endpoint}`);

  Object.keys(params).forEach((key) =>
    url.searchParams.append(key, params[key])
  );

  const accessToken = getCookie("accessToken");
  // const device_id = getCookie('deviceId');
// console.log(device_id, "::::::device_id")
  const isFormData = data instanceof FormData;
  const finalHeaders = {
    ...(!isFormData && { "Content-Type": "application/json" }),
    ...headers,
    cache: "no-store",
    accesstoken: accessToken ? `${accessToken?.value}` : "",
    // ['x-device-id']:device_id ? device_id?.value :""
  };

  try {
    const response = await fetch(url, {
      method,
      headers: finalHeaders,
      body: ["GET", "DELETE"].includes(method) ? null : JSON.stringify(data),
    });
    if (response.status === 403) {

      deleteCookie("accessToken");
      if (typeof window !== "undefined") {
        window.location.href = "/"; // Redirect to home page
      }

      return response;
    }
    if (response.status === 401) {

      deleteCookie("accessToken");
      if (typeof window !== "undefined") {
        window.location.href = "/"; // Redirect to home page
      }

      return response; // Optionally stop further execution after logout event
    }

    if (!response.ok) {
      if (response.status === 422) {
      
        deleteCookie("accessToken");
        if (typeof window !== "undefined") {
          window.location.href = "/"; // Redirect to home page
        }
        if (typeof window !== "undefined") {
          window.location.href = "/"; // Redirect to home page
        }
      }

      // throw new Error(result.message || "Something went wrong!");
    }

    return response;
  } catch (error) {
    console.error("API call error:", error);
    throw error;
  }
}

export const apiCalls = {
  get: (endpoint, config = {}) => apiCall("GET", endpoint, config),
  post: (endpoint, config = {}) => apiCall("POST", endpoint, config),
  put: (endpoint, config = {}) => apiCall("PUT", endpoint, config),
  delete: (endpoint, config = {}) => apiCall("DELETE", endpoint, config),
};
