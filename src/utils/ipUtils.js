import { useEffect } from "react";

let cachedIP = null;

export async function getIP() {
  if (cachedIP) {
    return cachedIP;
  }
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    cachedIP = data.ip; // Store just the IP string
    return cachedIP;
  } catch (error) {
    console.error("Failed to fetch IP:", error);
    return "0.0.0.0";
  }
}

export function useIP(setUserIp) {
  useEffect(() => {
    (async () => {
      try {
        const ip = await getIP();
        setUserIp(ip);
      } catch (error) {
        console.error("Failed to fetch IP:", error);
        setUserIp("0.0.0.0");
      }
    })();
  }, [setUserIp]);
} 