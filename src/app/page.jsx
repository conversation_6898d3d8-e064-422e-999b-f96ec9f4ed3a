import { getCookie } from "@/utils/cookiesHelper";
import LoggedInPage from "../components/LoggedInPage";
import NotLoggedInPage from "../components/NotLoggedInPage";
import { Suspense } from 'react'

export const dynamic = "force-dynamic";
export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  // Also supported by less commonly used
  // interactiveWidget: 'resizes-visual',
};

export const metadata = {
  robots: {
    index: true, // Allow search engines to index this page
    follow: true, // Allow search engines to follow links on this page
    nocache: false, // Allow caching of this page
    googleBot: {
      index: true,
      follow: true,
    },
  },
};

function LoadingFallback() {
  return <>Loding....</>
}

export default async function Home() {
  const userToken = getCookie("accessToken");


return <Suspense fallback={<LoadingFallback />}>{!userToken?.value ? <NotLoggedInPage /> : <LoggedInPage />}</Suspense>;

}