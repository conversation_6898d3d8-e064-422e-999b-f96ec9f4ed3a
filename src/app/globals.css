@import url("https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Saira:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap");

@tailwind base;
@tailwind components;
.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}
@tailwind utilities;


@layer base {
  :root {
    /* vegas color */
    --green-2: rgba(17, 115, 17, 1);
    --pink-1: rgba(255, 7, 70, 1);
    --pink-2: rgba(206, 41, 83, 1);
    --gray-1: rgba(126, 148, 136, 1);

    /* vegas color end */

    /* White and Black */
    --fta-whiteColor: rgba(255, 255, 255, 1);
    /* #ffffff */
    --fta-blackColor: #000000;
    /* Blue */
    --fta-blueColor: #018ff3;
    /* White Opacity */
    --fta-whiteOpacity-100: rgba(255, 255, 255, 0.1);
    --fta-whiteOpacity-200: rgba(255, 255, 255, 0.2);

    /* Black Opacity */
    --fta-blackOpacity-100: rgba(89, 81, 94, 1);
    /* #59515E */
    --fta-blackOpacity-150: rgba(59, 53, 61, 1);
    /* #3B353D */
    --fta-blackOpacity-200: rgba(0, 0, 0, 0.2);
    /* #00000033 */
    --fta-blackOpacity-250: rgba(11, 16, 32, 1);
    /* #1E1B1E */
    --fta-blackOpacity-300: rgba(89, 81, 94, 0.3);
    /* #59515E */
    --fta-blackOpacity-350: rgba(109, 108, 124, 1);
    /* #776C7C */
    --fta-blackOpacity-400: rgba(147, 134, 152, 1);
    /* #938698 */
    --fta-blackOpacity-500: rgba(173, 160, 178, 1);
    /* #ADA0B2 */
    --fta-blackOpacity-550: rgba(59, 53, 61, 0.5);
    /* #3B353D */
    --fta-blackOpacity-600: rgba(26, 24, 44, 1);
    /* Primary Color */
    --fta-primary-200: rgba(160, 122, 176, 1);
    /* #A07AB0 */
    --fta-primary-250: rgba(50, 46, 51, 0.25);
    /* #322E33 */
    --fta-primary-300: rgba(192, 167, 202, 1);
    /* #C0A7CA */
    --fta-primary-400: rgba(223, 211, 229, 1);
    /* #DFD3E5 */
    --fta-primary-500: rgba(129, 78, 149, 1);
    /* #814E95 */
    --fta-primary-600: rgba(129, 78, 149, 1);
    /* #1A1D2C */
    --fta-primary-1000: rgba(26, 29, 44, 1);
    /* #5E2278 */

    /* Secondary Color */
    --fta-secondary-300: rgba(248, 242, 163, 1);
    /* #F8F2A3 */
    --fta-secondary-500: rgba(201, 173, 0, 1);
    /* #C9AD00 */
    --fta-secondary-800: rgba(254, 193, 2, 1);
    /* #E3D61C */
    --fta-secondary-1000: rgba(227, 214, 28, 1);
    /* #E3D61C */
  }

  @font-face {
    font-family: "geometrics-medium";
    font-style: medium;
    font-weight: 500;
    font-display: swap;
    src: url(/fonts/geometrics/geoslab703-md-bt-medium.ttf) format("truetype");
  }

  @font-face {
    font-family: "geometrics-bold";
    font-style: bold;
    font-weight: 700;
    font-display: swap;
    src: url(/fonts/geometrics/geoslab703-md-bt-bold.ttf) format("truetype");
  }
}

@layer components {
  body {
    @apply !font-manrope;
  }

  /* Category select filed */
  .category-select {
    @apply min-w-[190px] max-md:min-w-[155px];
  }

  .category-select>.category__control {
    @apply bg-primary-100 border border-solid border-transparent focus:border focus:border-solid leading-none focus:border-gray-200 cursor-pointer rounded-lg;
  }

  .category-select>.category__control:hover {
    @apply border border-solid border-transparent;
  }

  .category-select>.category__control.category__control--is-focused {
    @apply outline-none border border-solid border-gray-200 shadow-none;
  }

  .category-select>.category__control>.category__value-container {
    @apply px-4 max-md:px-3 py-2 max-md:py-1;
  }

  .category-select>.category__control>.category__value-container>.category__input-container {
    @apply text-white text-sm;
  }

  .category-select>.category__control>.category__value-container>.category__input-container>.category__input {
    @apply text-white;
  }

  .category-select>.category__control>.category__value-container>.category__single-value {
    @apply text-white leading-tight text-sm;
  }

  .category-select>.category__control>.category__indicators>.category__indicator-separator {
    @apply hidden;
  }

  .category-select>.category__control>.category__value-container>.category__placeholder {
    @apply text-white text-sm;
  }

  .category-select>.category__menu {
    @apply bg-primary-100 rounded-md;

  }

  .category-select>.category__menu>.category__menu-list {
    @apply p-2;
    
  }

  .category-select>.category__menu>.category__menu-list>.category__option {
    @apply text-white text-sm;
  }

  .category-select>.category__menu>.category__menu-list>.category__option:hover {
    @apply bg-gray-500;
  }

  .category-select>.category__menu>.category__menu-list>.category__option.category__option--is-selected {
    @apply bg-green-500 rounded-md;
  }

  .category-select>.category__menu>.category__menu-list>.category__option--is-focused {
    @apply bg-transparent rounded-md;
  }

  .category-select>.category__control>.category__indicators>.category__indicator>svg {
    @apply text-green-4;
  }

  /* Category select filed END */

  /* Coin select filed */
  .coin-select>.coin__control {
    @apply bg-gray-500 rounded-[0.313rem] focus:ring-2 focus:bg-[rgba(62,62,62,0.3)] border-gray-500 focus:border outline-none shadow-none focus:border-green-100 h-[2.875rem];
  }

  .coin-select>.coin__control:hover {
    @apply border border-solid border-transparent;
  }

  .coin-select>.coin__control.coin__control--is-focused {
    @apply outline-none border-[2px] border-solid border-green-100;
  }

  .coin-select>.coin__control>.coin__value-container {
    @apply px-4 py-2;
  }

  .coin-select>.coin__control>.coin__value-container>.coin__input-container>.coin__input {
    @apply text-gray-2;
  }

  .coin-select>.coin__control>.coin__value-container>.coin__single-value {
    @apply text-gray-2 leading-tight;
  }

  .coin-select>.coin__control>.coin__indicators>.coin__indicator-separator {
    @apply hidden;
  }

  .coin-select>.coin__control>.coin__value-container>.coin__placeholder {
    @apply text-green-4 text-sm font-bold max-w-36 truncate;
  }

  .coin-select>.coin__menu {
    @apply bg-gray-500 rounded-md;
  }

  .coin-select>.coin__menu>.coin__menu-list {
    @apply p-2;
  }

  .coin-select>.coin__menu>.coin__menu-list>.coin__option {
    @apply text-green-4 text-sm font-bold;
  }

  .coin-select>.coin__menu>.coin__menu-list>.coin__option.coin__option--is-selected {
    @apply bg-green-500 rounded-md;
  }

  .coin-select>.coin__menu>.coin__menu-list>.coin__option:hover {
    @apply bg-primary-100;
  }

  .coin-select>.coin__menu>.coin__menu-list>.coin__option--is-focused {
    @apply bg-transparent rounded-md;
  }

  .coin-select>.coin__control>.coin__indicators>.coin__indicator>svg {
    @apply text-green-4;
  }

  /* Coin select filed END */

  /* FORM select filed */

  .form-select>.form__control {
    @apply bg-gray-500 border border-solid border-transparent focus:border focus:border-solid leading-none focus:border-gray-200 cursor-pointer rounded-[0.313rem] h-[2.875rem];
  }

  .form-select>.form__control:hover {
    @apply border border-solid border-transparent;
  }

  .form-select>.form__control.form__control--is-focused {
    @apply outline-none border border-solid border-gray-200 shadow-none;
  }

  .form-select>.form__control>.form__value-container {
    @apply px-4 py-2;
  }

  .form-select.form__value__phone>.form__control>.form__value-container {
    @apply px-1 py-2;
  }

  .form-select>.form__control>.form__value-container>.form__input-container>.category__input {
    @apply text-gray-2;
  }

  .form-select>.form__control>.form__value-container>.form__single-value {
    @apply text-gray-2 leading-tight;
  }

  .form-select>.form__control>.form__indicators>.form__indicator-separator {
    @apply hidden;
  }

  .form-select>.form__control>.form__value-container>.form__placeholder {
    @apply font-bold text-green-4 text-[0.875rem];
  }

  .form-select>.form__menu {
    @apply bg-primary-100 rounded-md;
  }

  .form-select>.form__menu>.form__menu-list {
    @apply p-2;
  }

  .form-select>.form__menu>.form__menu-list>.form__option:hover {
    @apply bg-gray-500;
  }

  .form-select>.form__menu>.form__menu-list>.form__option {
    @apply text-white text-sm;
  }

  .form-select>.form__menu>.form__menu-list>.form__option.form__option--is-selected {
    @apply bg-green-500 rounded-md;
  }

  .form-select>.form__menu>.form__menu-list>.form__option--is-focused {
    @apply bg-transparent rounded-md;
  }

  .form-select>.form__control>.form__indicators>.form__indicator>svg {
    @apply text-green-4;
  }

  /* FORM select filed END */

  /* Provider select field */

  .provider-select>.form__control {
    @apply bg-primary-100 border border-solid border-transparent focus:border focus:border-solid leading-none focus:border-gray-200 cursor-pointer rounded-[0.313rem] min-w-[10.875rem] h-[2.875rem];
  }

  .provider-select>.form__control:hover {
    @apply border border-solid border-transparent;
  }

  .provider-select>.form__control.form__control--is-focused {
    @apply outline-none border border-solid border-gray-200 shadow-none;
  }

  .provider-select>.form__control>.form__value-container {
    @apply px-4 py-2;
  }

  .provider-select.form__value__phone>.form__control>.form__value-container {
    @apply px-1 py-2;
  }

  .provider-select>.form__control>.form__value-container>.form__input-container>.category__input {
    @apply text-gray-2;
  }

  .provider-select>.form__control>.form__value-container>.form__single-value {
    @apply text-gray-2 leading-tight;
  }

  .provider-select>.form__control>.form__indicators>.form__indicator-separator {
    @apply hidden;
  }

  .provider-select>.form__control>.form__value-container>.form__placeholder {
    @apply font-bold text-green-4 text-[0.875rem];
  }

  .provider-select>.form__menu {
    @apply bg-primary-100 rounded-md;
  }

  .provider-select>.form__menu>.form__menu-list {
    @apply p-2;
  }

  .provider-select>.form__menu>.form__menu-list>.form__option:hover {
    @apply bg-gray-500;
  }

  .provider-select>.form__menu>.form__menu-list>.form__option {
    @apply text-white text-sm;
  }

  .provider-select>.form__menu>.form__menu-list>.form__option.form__option--is-selected {
    @apply bg-green-500 rounded-md;
  }

  .provider-select>.form__menu>.form__menu-list>.form__option--is-focused {
    @apply bg-transparent rounded-md;
  }

  .provider-select>.form__control>.form__indicators>.form__indicator>svg {
    @apply text-green-4;
  }

  /* Feedback select filed */

  .faq-select>.faq-inner__control {
    @apply bg-primary-100 border border-solid border-transparent focus:border focus:border-solid leading-none focus:border-gray-200 cursor-pointer rounded-md h-[2.875rem];
  }

  .faq-select>.faq-inner__control:hover {
    @apply border border-solid border-transparent;
  }

  .faq-select>.faq-inner__control.faq-inner__control--is-focused {
    @apply outline-none border border-solid border-gray-200 shadow-none;
  }

  .faq-select>.faq-inner__control>.faq-inner__value-container {
    @apply px-3.5 py-2;
  }

  /* .faq-select>.faq-inner__control>.faq-inner__value-container>.faq-inner__input-container>.category__input {
    @apply text-gray-2;
  } */

  .faq-select>.faq-inner__control>.faq-inner__value-container>.faq-inner__single-value {
    @apply text-gray-2 leading-tight text-start text-sm font-bold;
  }

  .faq-select>.faq-inner__control>.faq-inner__indicators>.faq-inner__indicator-separator {
    @apply hidden;
  }

  .faq-select>.faq-inner__control>.faq-inner__value-container>.faq-inner__placeholder {
    @apply font-bold text-green-4 text-[0.875rem];
  }

  .faq-select>.faq-inner__menu {
    @apply bg-primary-100 rounded-md;
  }

  .faq-select>.faq-inner__menu>.faq-inner__menu-list {
    @apply p-2;
  }

  .faq-select>.faq-inner__menu>.faq-inner__menu-list>.faq-inner__option {
    @apply text-gray-2 text-sm text-start cursor-pointer;
  }

  .faq-select>.faq-inner__menu>.faq-inner__menu-list>.faq-inner__option.faq-inner__option--is-selected {
    @apply bg-green-500 rounded-md;
  }

  .faq-select>.faq-inner__menu>.faq-inner__menu-list>.faq-inner__option--is-focused {
    @apply bg-transparent rounded-md;
  }

  .faq-select>.faq-inner__control>.faq-inner__indicators>.faq-inner__indicator>svg {
    @apply text-green-4;
  }

  /* Feedback select filed END */

  /* Body Scrollbar */

  *::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  *::-webkit-scrollbar-thumb {
    @apply bg-borderBg rounded-md border-[2px] border-solid border-blackOpacity-150;
  }

  *::-webkit-scrollbar-track {
    @apply bg-blackOpacity-150 rounded-md;
  }

  body::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  body::-webkit-scrollbar-thumb {
    @apply bg-borderBg rounded-md border-[2px] border-solid border-blackOpacity-150;
  }

  body::-webkit-scrollbar-track {
    @apply bg-blackOpacity-150 rounded-md;
  }

  /* Profile Scrollbar */

  .profile-scrollbar::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .profile-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-borderBg rounded-md border-[2px] border-solid border-blackOpacity-150;
  }

  .profile-scrollbar::-webkit-scrollbar-track {
    @apply bg-blackOpacity-150 rounded-md;
  }

  .custom-datepicker button {
    @apply bg-black-3;
  }

  input::placeholder {
    @apply font-bold text-green-4 text-[0.875rem];
  }

  .tier-1 {
    @apply max-2xl:h-[123px] h-[153px];
  }

  .tier-2 {
    @apply max-2xl:h-[200px] h-[230px];
  }

  .tier-3 {
    @apply max-2xl:h-[275px] h-[305px];
  }

  .tier-4 {
    @apply max-2xl:h-[352px] h-[382px];
  }

  .tier-5 {
    @apply max-2xl:h-[505px] h-[535px];
  }
}

@layer utilities {
  .ribbon-clip {
    clip-path: polygon(100% 0%, 90% 50%, 100% 100%, 0 100%, 0% 50%, 0 0);
  }

  .bg-custom-gradient {
    background-size: 3em 3em;
    background-color: #212537;
    opacity: 1;
  }
}

body {
  /* font-family: "Mulish", sans-serif !important; */
  font-family: "Manrope", sans-serif;
  /* background: var(--fta-blackOpacity-250); */
  background: rgba(11, 16, 32, 1);
}

button,
span,
p,
h1,
h2,
h3,
h4,
h5,
h6,
label,
input,
select,
textarea,
a,
table {
  font-family: "Manrope", sans-serif;
}

.borderText {
  text-transform: uppercase;
  font-weight: 900;
  color: #432c29;
  -webkit-text-stroke: 3px transparent;
  /* background: linear-gradient(
    87.99deg,
    #ebdc03 -4.33%,
    #ff9321 21.23%,
    #ff9321 23.32%,
    #f24c50 47.31%,
    #8b21b7 100%
  ); */
  background-clip: text;
  -webkit-background-clip: text;
}

.maskImg::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-mask-image: linear-gradient(#0000, rgb(0 0 0));
}

.signupform {
  --s: 35px;
  /* control the size*/
  --c1: #212537;
  --c2: #131625;

  --_g: var(--c2) 6% 14%, var(--c1) 16% 24%, var(--c2) 26% 34%,
    var(--c1) 36% 44%, var(--c2) 46% 54%, var(--c1) 56% 64%, var(--c2) 66% 74%,
    var(--c1) 76% 84%, var(--c2) 86% 94%;
  background: radial-gradient(100% 100% at 100% 0,
      var(--c1) 4%,
      var(--_g),
      #0008 96%,
      #0000),
    radial-gradient(100% 100% at 0 100%,
      #0000,
      #0008 4%,
      var(--_g),
      var(--c1) 96%) var(--c1);
  background-size: var(--s) var(--s);
}

/* LOADING */

.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 10px solid #ddd;
  border-top-color: rgb(61, 255, 161);
  animation: loading 1s linear infinite;
}

@keyframes loading {
  to {
    transform: rotate(360deg);
  }
}

#rcc-decline-button {
  background: transparent !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  height: 36px !important;
  line-height: 1 !important;
  padding: 9px 0 !important;
  text-align: center !important;
  text-transform: uppercase !important;
  width: 122px !important;
  border: 2px solid #007ad0 !important;
  color: #007ad0 !important;
}

input#date {
  color: white !important;
}

@layer components {

  /* General input styles */
  input {
    @apply bg-blackOpacity-250 text-white text-sm rounded-md block w-full px-4 py-3 border-2 border-solid border-transparent focus:border-green-300 focus:bg-gray-8 focus:outline-none;
  }

  input::placeholder {
    @apply font-bold text-green-4 text-[0.875rem];
  }

  /* Autofill Styles */
  input:-webkit-autofill {
    @apply bg-blackOpacity-250 text-white !important;
    /* Ensure background and text color matches */
    -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.8) inset !important;
    /* Set the background */
    -webkit-text-fill-color: #ffffff !important;
    /* Set the text color */
    box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.8) inset !important;
    /* Fallback for other browsers */
  }

  input:-moz-autofill {
    @apply bg-blackOpacity-250 text-white !important;
    /* Background and text color */
    box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.8) inset !important;
    /* Fallback for Firefox */
    -moz-text-fill-color: #ffffff !important;
    /* Set text color for Firefox */
  }

  textarea:-webkit-autofill {
    @apply bg-blackOpacity-250 text-white !important;
    /* Same background for textarea */
    -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.8) inset !important;
    -webkit-text-fill-color: #ffffff !important;
  }

  /* Optional: Handle border and shadow on focus for all input types */
  input:focus,
  textarea:focus {
    @apply focus:border-green-300 focus:bg-gray-8 focus:outline-none;
  }
}

@media only screen and (max-width: 768px) {
  .CookieConsent {
    bottom: 70px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    left: 52% !important;
    transform: translateX(-16px);
  }

  .CookieConsent button {
    height: 30px !important;
    line-height: 1 !important;
    width: 100px !important;
    margin: 8px !important;
  }
}

p {
  margin-bottom: 20px;
}

#gtm_hide {
  display: none !important;
  position: absolute;
  left: -5000px;
  visibility: hidden;
}

#gtm-script {
  display: none !important;
  visibility: hidden;
}
.tw-idverif-scope {
  z-index: 999999999;
  position: fixed;
  background-color: rgba(255, 255, 255, 0.5);
}

/* @media screen and (-webkit-min-device-pixel-ratio:0) { 
  select,
  textarea,
  input {
    font-size: 16px;
  }

  .signupform{
    font-size: 16px;
  }

  input[type='text'],textarea {font-size:1em;}

} */

/* input[type='text'],textarea {font-size:1em;} */


/* hide default clear('X') in search  */

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}