# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/.well-known
/public/.well-known
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.pnpm-lock.yaml

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.pnpm-store

# Sentry Config File
.env.sentry-build-plugin
